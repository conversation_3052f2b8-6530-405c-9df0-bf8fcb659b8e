```wavedrom
//tcsrr.r
{"reg": [
    {"bits": 7,  "name": 'ACE_op',    "attr": '1111011'},
    {"bits": 5,  "name": 'rd',    "attr": ''},
    {"bits": 3,  "name": 'tuop',     "attr": '100'},
    {"bits": 5,  "name": '',       "attr": '00000'},
    {"bits": 9,  "name": 'csr_addr[10:2]',       "attr": ''},
    {"bits": 1,  "name": 'b64', "attr":''},
    {"bits": 2,  "name": 'rw',    "attr": '01'},
],config:{"bits":32,lanes:1,hspace: 1500}}
```