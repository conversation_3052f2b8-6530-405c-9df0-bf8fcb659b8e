﻿Field,Start bit,End bit,# bits,DataType,Tensormap Description,Notes
tensorDataType,0,7,8,uint8,Tensor data type ID. Range: 0~255.,
tensorRank,8,15,8,uint8,number of tensor dimensions. Valid: 1~5,
tensorTiled,16,23,8,boolean,"Tensor layout is tiled or not. Valid: {0: linear, 1: tiled}",
(Reserved),24,31,8
tileDim,32,96,64,uint32*2,"2D logical Tile size (in elements), in the lowest two tensor dimensions. (For linear format tensor, only used as src or dst tile shape for linear <-> tiled format conversion). Valid(for 32bit/16bit/8bit/6bit/4bit): [8/16/32/128/64, 32], [16/32/64/256/128, 16], [32/64/128/512/256, 8], [256/512/1024/3072/2048, 1]",
tileHeaderSize,96,127,32,uint32,"Header size (in byte) for each Tile. Range: 0~1024. Valid (default/6bit): 256/512",
tileDataSize,128,159,32,uint32,"Data size (in byte) for each Tile. Range: 1~4096. Valid (default/6bit): 1024/3072",
superTileDim,160,175,16,uint8*2,"2D superTile layout (in tiles along each dimension). (For linear tensormap, only used for linear -> tiled format conversion). Range (per dim): 1~2^8. Valid: MX: [4,1] (typical), [2,2], [1,4], NonMX: [1,1]",
(Reserved),176,191,16
globalAddress,192,255,64,uint64,"Tensor's start address in global/host memory (byte, virtual). 256B aligned.",
globalDim,256,415,160,uint32*5,"Array containing tensor size (in Tiles) along each of the tensorRank dimensions. Range: 1~2^32",
globalStrides,416,575,160,uint32*5,"Array containing tensor size (in Tiles) along each of the tensorRank dimensions. Range: 1~2^32",
boxDim,576,735,160,uint32*5,"Array containing traversal box size (in Tiles) along each of the tensorRank dimensions. Require: <=globalDim in each dim. Range: 1~2^16",
oobFillValue,736,767,32,uint32,User-defined value to fill out-of-bound regions in the copy box.,
Reserved,,,256,,,
Total,,,1024,,,
