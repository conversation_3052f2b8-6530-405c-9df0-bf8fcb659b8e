#!/usr/bin/env python3

def generate_correct_tmul_ttr():
    """生成正确的tmul.ttr.f32 T1,T4,a0指令编码"""
    
    # 基于tmul.ttr.md的wavedrom定义
    # 第一个word (32位)
    word1 = 0
    
    # 按照wavedrom从LSB到MSB的顺序
    pos = 0
    
    # ACE_op[6:0] = 1111011
    word1 |= (0b1111011 << pos)
    pos += 7
    
    # bits[8:7] = 01
    word1 |= (0b01 << pos)
    pos += 2
    
    # vecuop1[10:9] = 10
    word1 |= (0b10 << pos)
    pos += 2
    
    # tmask[11] = 0 (假设)
    word1 |= (0 << pos)
    pos += 1
    
    # tuop[14:12] = 110
    word1 |= (0b110 << pos)
    pos += 3
    
    # rs2[19:15] = 01010 (a0 = x10)
    word1 |= (10 << pos)
    pos += 5
    
    # bits[24:20] = 00000
    word1 |= (0b00000 << pos)
    pos += 5
    
    # reuse1[25] = 0 (假设)
    word1 |= (0 << pos)
    pos += 1
    
    # bit[26] = 0
    word1 |= (0 << pos)
    pos += 1
    
    # neg1[27] = 0 (假设)
    word1 |= (0 << pos)
    pos += 1
    
    # neg2[28] = 0 (假设)
    word1 |= (0 << pos)
    pos += 1
    
    # vecuop2[1:0][30:29] = 00 (f32)
    word1 |= (0b00 << pos)
    pos += 2
    
    # bit[31] = 0
    word1 |= (0 << pos)
    pos += 1
    
    print(f"Word 1: 0x{word1:08x} = {word1:032b}")
    
    # 第二个word (32位)
    word2 = 0
    pos = 0
    
    # ACE_op[6:0] = 1111011 (修正：应该是0x7B而不是0x6B)
    word2 |= (0b1111011 << pos)
    pos += 7
    
    # bit[7] = 0
    word2 |= (0 << pos)
    pos += 1
    
    # vecuop2[5:2][11:8] = 0001
    word2 |= (0b0001 << pos)
    pos += 4
    
    # tuop[14:12] = 010
    word2 |= (0b010 << pos)
    pos += 3
    
    # Ts1[22:15] = 00000100 (T4)
    word2 |= (4 << pos)
    pos += 8
    
    # Td[30:23] = 00000001 (T1)
    word2 |= (1 << pos)
    pos += 8
    
    # bit[31] = 0
    word2 |= (0 << pos)
    pos += 1
    
    print(f"Word 2: 0x{word2:08x} = {word2:032b}")
    
    # 组合成64位指令
    instruction = (word2 << 32) | word1
    print(f"完整指令: 0x{instruction:016x}")
    
    return instruction

def test_generated_instruction():
    """测试生成的指令是否能正确反汇编"""
    
    from tile_disassembler import TileDisassembler
    
    correct_instr = generate_correct_tmul_ttr()
    
    print(f"\n测试生成的指令:")
    d = TileDisassembler()
    result = d.disassemble_hex_string(f"0x{correct_instr:016x}")
    
    if result:
        print(f"反汇编结果: {result}")
        if "tmul.ttr" in result:
            print("✓ 成功！指令正确解码为tmul.ttr")
        else:
            print("✗ 解码为其他指令")
    else:
        print("✗ 无法解码")

def compare_instructions():
    """比较原始指令和正确指令的差异"""
    
    original = 0x82216b000564fb
    correct = generate_correct_tmul_ttr()
    
    print(f"\n指令比较:")
    print(f"原始指令: 0x{original:016x}")
    print(f"正确指令: 0x{correct:016x}")
    
    diff = original ^ correct
    print(f"差异位:   0x{diff:016x}")
    
    # 分析差异
    word1_orig = original & 0xFFFFFFFF
    word1_corr = correct & 0xFFFFFFFF
    word2_orig = (original >> 32) & 0xFFFFFFFF
    word2_corr = (correct >> 32) & 0xFFFFFFFF
    
    print(f"\nWord 1 差异:")
    print(f"  原始: 0x{word1_orig:08x}")
    print(f"  正确: 0x{word1_corr:08x}")
    print(f"  差异: 0x{(word1_orig ^ word1_corr):08x}")
    
    print(f"\nWord 2 差异:")
    print(f"  原始: 0x{word2_orig:08x}")
    print(f"  正确: 0x{word2_corr:08x}")
    print(f"  差异: 0x{(word2_orig ^ word2_corr):08x}")
    
    # 主要差异是第二个word中的ACE_op字段
    ace_op2_orig = word2_orig & 0x7F
    ace_op2_corr = word2_corr & 0x7F
    print(f"\n第二个ACE_op字段:")
    print(f"  原始: {ace_op2_orig:07b} = 0x{ace_op2_orig:02x}")
    print(f"  正确: {ace_op2_corr:07b} = 0x{ace_op2_corr:02x}")

if __name__ == "__main__":
    compare_instructions()
    test_generated_instruction()
