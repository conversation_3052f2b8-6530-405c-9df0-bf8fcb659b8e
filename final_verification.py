#!/usr/bin/env python3

def simulate_systemverilog_logic():
    """Simulate the complete SystemVerilog logic with corrected fields"""
    
    print("=== Final Verification of SystemVerilog Logic ===")
    print()
    
    # Test instruction: 0x2337b0104627b
    hex_val = 0x2337b0104627b
    print(f"Test instruction: 0x{hex_val:016x}")
    
    word1 = hex_val & 0xFFFFFFFF
    word2 = (hex_val >> 32) & 0xFFFFFFFF
    
    print(f"Word 1: 0x{word1:08x}")
    print(f"Word 2: 0x{word2:08x}")
    print()
    
    # Step 1: get_instruction_length
    ace_op = word1 & 0x7F
    tuop = (word1 >> 12) & 0x7
    
    print("Step 1: get_instruction_length")
    print(f"  ACE_OP: {ace_op} (should be 123)")
    print(f"  TUOP: {tuop} (should be 6)")
    
    if ace_op == 123 and tuop == 6:
        length = "INSTR_64BIT"
        print(f"  ✓ Detected length: {length}")
    else:
        print("  ✗ Failed to detect as 64-bit tile instruction")
        return
    
    print()
    
    # Step 2: extract_instruction_name
    print("Step 2: extract_instruction_name")
    
    memuop = (word1 >> 25) & 0x3F
    tuop_second = (word2 >> 12) & 0x7
    
    print(f"  memuop: {memuop} (should be 0)")
    print(f"  tuop_second: {tuop_second} (should be 3)")
    
    if tuop == 6 and memuop == 0 and tuop_second == 3:
        print("  ✓ Detected as tmma instruction (tuop_110 + tuop_011)")
        
        # Step 3: extract_tmma_instruction_name
        print()
        print("Step 3: extract_tmma_instruction_name")
        
        # Extract fields with corrected understanding
        memb = (word1 >> 7) & 0x1
        mop_5 = (word1 >> 9) & 0x1
        mop_4_0 = (word1 >> 26) & 0x1F
        mop_full = (mop_5 << 5) | mop_4_0
        reuse = (word2 >> 8) & 0x3
        ashape = (word2 >> 10) & 0x3      # r32/r16/r8
        micro_num = (word1 >> 23) & 0x3   # m1/m2/m4
        mema = (word2 >> 31) & 0x1
        
        print(f"  memb: {memb}")
        print(f"  mema: {mema}")
        print(f"  mop_full: {mop_full}")
        print(f"  reuse: {reuse}")
        print(f"  ashape: {ashape} (r32/r16/r8)")
        print(f"  micro_num: {micro_num} (m1/m2/m4)")
        
        # Determine variant
        if mema == 0 and memb == 0:
            variant = "tmma.ttt"
        elif mema == 0 and memb == 1:
            variant = "tmma.ttr"
        elif mema == 1 and memb == 0:
            variant = "tmma.trt"
        else:
            variant = "tmma.trr"
        
        # Determine data type
        data_types = {32: "f32.mxint8.mxint8", 0: "tf32.tf32.f32"}
        data_type = data_types.get(mop_full, f"mop_{mop_full}")
        
        # Determine ashape (r32/r16/r8)
        ashape_strs = {0: "r32", 1: "r16", 2: "r8"}
        ashape_str = ashape_strs.get(ashape, f"ashape_{ashape}")
        
        # Determine micro_num (m1/m2/m4)
        micro_strs = {0: "m1", 1: "m2", 2: "m4"}
        micro_str = micro_strs.get(micro_num, f"micro_{micro_num}")
        
        # Determine reuse
        reuse_strs = {0: "r32", 1: "reused", 2: "reused", 3: "reused"}
        reuse_str = reuse_strs.get(reuse, f"reuse_{reuse}")
        
        # Build instruction name
        instruction_name = f"{variant}.{data_type}.{ashape_str}.{micro_str}.{reuse_str}"
        
        print(f"  ✓ Generated instruction name: {instruction_name}")
        
    else:
        print("  ✗ Not detected as tmma instruction")
        return
    
    print()
    
    # Step 4: format_operands
    print("Step 4: format_operands")
    
    # Extract operands
    td = (word2 >> 23) & 0xFF
    ts1 = (word2 >> 15) & 0xFF
    ts2 = (word1 >> 15) & 0xFF
    
    print(f"  Td: {td}")
    print(f"  Ts1: {ts1}")
    print(f"  Ts2: {ts2}")
    print(f"  memb: {memb} (determines operand format)")
    
    if memb == 0:
        operands = f"T{td}, T{ts1}, T{ts2}"
    else:
        rs2 = (word1 >> 20) & 0x1F
        operands = f"T{td}, T{ts1}, x{rs2}"
    
    print(f"  ✓ Generated operands: {operands}")
    
    print()
    
    # Final result
    print("=== FINAL RESULT ===")
    full_instruction = f"{instruction_name} {operands}"
    expected = "tmma.ttt.f32.mxint8.mxint8.r32.m4.reused T0,T4,T8"
    
    print(f"Generated: {full_instruction}")
    print(f"Expected:  {expected}")
    print()
    
    # Compare
    generated_clean = full_instruction.replace(" ", "")
    expected_clean = expected.replace(" ", "")
    
    if generated_clean == expected_clean:
        print("✅ PERFECT MATCH!")
        print("✅ SystemVerilog logic is working correctly")
        print("✅ The instruction will no longer be 'tld_64bit_unimplemented'")
    else:
        print("❌ Mismatch detected")
        print(f"Generated (clean): {generated_clean}")
        print(f"Expected (clean):  {expected_clean}")
    
    print()
    print("=== SUMMARY OF FIXES ===")
    print("1. ✅ Added tmma detection in tuop_110 handling")
    print("2. ✅ Created extract_tmma_instruction_name function")
    print("3. ✅ Corrected field interpretation:")
    print("   - ashape → r32/r16/r8 (not m8/m16/m4)")
    print("   - micro_num → m1/m2/m4 (from word1[24:23])")
    print("4. ✅ Added tmma operand formatting")
    print("5. ✅ Instruction now correctly identified as tmma instead of tld_64bit_unimplemented")

if __name__ == '__main__':
    simulate_systemverilog_logic()
