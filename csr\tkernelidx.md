```wavedrom
//tkernel_idx
{"reg": [
    {"bits": 4,  "name": 'thread_idx', "attr": 'RO'},
    {"bits": 12,  "name": 'tb_idx_x', "attr": 'RO'},
    {"bits": 8,  "name": 'tb_idx_y', "attr": 'RO'},
    {"bits": 8,  "name": 'tb_idx_z', "attr": 'RO'},
    {"bits": 16,  "name": 'tbc_idx_x', "attr": 'RO'},
    {"bits": 8,  "name": 'tbc_idx_y', "attr": 'RO'},
    {"bits": 8,  "name": 'tbc_idx_z', "attr": 'RO'},
], config: {"bits":64,lanes:2,hspace: 1500}}
```