#!/usr/bin/env python3

def check_hex_interpretation():
    """Check if we're interpreting the hex value correctly"""
    
    # The user mentioned 0x2337b0104627b
    # Let me check if this is the correct interpretation
    
    hex_str = "2337b0104627b"
    print(f"Original hex string: {hex_str}")
    
    # Try different interpretations
    hex_val1 = int(hex_str, 16)
    print(f"Interpretation 1: 0x{hex_val1:016x}")
    print(f"Binary: {hex_val1:064b}")
    print()
    
    # Maybe it should be interpreted as a full 64-bit value with leading zeros
    hex_val2 = int(f"0{hex_str}", 16)
    print(f"Interpretation 2: 0x{hex_val2:016x}")
    print(f"Binary: {hex_val2:064b}")
    print()
    
    # Let's also check the expected operands T0, T4, T8
    # T0 = 0, T4 = 4, T8 = 8
    print("Expected operands:")
    print(f"T0: {0:08b}")
    print(f"T4: {4:08b}")
    print(f"T8: {8:08b}")
    print()
    
    # Let's see if we can find these patterns in the instruction
    word1 = hex_val1 & 0xFFFFFFFF
    word2 = (hex_val1 >> 32) & 0xFFFFFFFF
    
    print(f"Word 1: 0x{word1:08x} = {word1:032b}")
    print(f"Word 2: 0x{word2:08x} = {word2:032b}")
    print()
    
    # Look for T0 (0), T4 (4), T8 (8) in various 8-bit fields
    print("Searching for T0, T4, T8 patterns in 8-bit fields:")
    for i in range(0, 64, 8):
        field_val = (hex_val1 >> i) & 0xFF
        print(f"  Bits {i+7}:{i} = {field_val:08b} = T{field_val}")
        if field_val in [0, 4, 8]:
            print(f"    ✓ Found T{field_val}!")

    print()
    print("Let me also check 5-bit fields for smaller register numbers:")
    for i in range(0, 64, 5):
        if i + 4 < 64:
            field_val = (hex_val1 >> i) & 0x1F
            if field_val in [0, 4, 8]:
                print(f"  Bits {i+4}:{i} = {field_val:05b} = {field_val}")

    print()
    print("Detailed bit-by-bit analysis:")
    print(f"Bits 63:56 = {(hex_val1 >> 56) & 0xFF:08b} = {(hex_val1 >> 56) & 0xFF}")
    print(f"Bits 55:48 = {(hex_val1 >> 48) & 0xFF:08b} = {(hex_val1 >> 48) & 0xFF}")
    print(f"Bits 47:40 = {(hex_val1 >> 40) & 0xFF:08b} = {(hex_val1 >> 40) & 0xFF}")
    print(f"Bits 39:32 = {(hex_val1 >> 32) & 0xFF:08b} = {(hex_val1 >> 32) & 0xFF}")
    print(f"Bits 31:24 = {(hex_val1 >> 24) & 0xFF:08b} = {(hex_val1 >> 24) & 0xFF}")
    print(f"Bits 23:16 = {(hex_val1 >> 16) & 0xFF:08b} = {(hex_val1 >> 16) & 0xFF}")
    print(f"Bits 15:8  = {(hex_val1 >> 8) & 0xFF:08b} = {(hex_val1 >> 8) & 0xFF}")
    print(f"Bits 7:0   = {(hex_val1 >> 0) & 0xFF:08b} = {(hex_val1 >> 0) & 0xFF}")
    
    print()
    
    # Let's also check if the mop field should give us mxint8.mxint8.f32 (mop=32)
    # Maybe the mop field is in a different location
    print("Checking different mop field locations:")
    
    # Current interpretation: mop[5] at bit 10, mop[4:0] at bits 30:25
    mop_current = ((word1 >> 10) & 0x1) << 5 | ((word1 >> 25) & 0x1F)
    print(f"Current mop interpretation: {mop_current}")
    
    # Maybe mop is entirely in bits 30:25 of first word
    mop_alt1 = (word1 >> 25) & 0x3F
    print(f"Alternative mop (bits 30:25): {mop_alt1}")
    
    # Maybe mop is in second word
    mop_alt2 = (word2 >> 25) & 0x3F
    print(f"Alternative mop (word2 bits 30:25): {mop_alt2}")
    
    # Check if any of these gives us 32 (for mxint8.mxint8.f32)
    if mop_alt1 == 32:
        print("✓ Found mop=32 in word1 bits 30:25!")
    elif mop_alt2 == 32:
        print("✓ Found mop=32 in word2 bits 30:25!")

if __name__ == '__main__':
    check_hex_interpretation()
