#!/usr/bin/env python3

def test_systemverilog_logic():
    """Test the SystemVerilog logic for tmma instruction detection"""
    
    # Test instruction: 0x2337b0104627b
    hex_val = 0x2337b0104627b
    print(f"Testing SystemVerilog logic for: 0x{hex_val:016x}")
    
    # Simulate the SystemVerilog get_instruction_length function
    word1 = hex_val & 0xFFFFFFFF
    ace_op = word1 & 0x7F
    tuop = (word1 >> 12) & 0x7
    
    print(f"ACE_OP: {ace_op} (should be 123)")
    print(f"TUOP: {tuop} (should be 6)")
    
    if ace_op == 123:  # TILE_ACE_OP
        if tuop == 6:  # tuop_110
            print("✓ Would be detected as tuop_110")
            return "INSTR_64BIT"
        elif tuop == 1:  # tuop_001
            print("Would be detected as tuop_001 (simple tmma.ttt)")
            return "INSTR_64BIT"
    
    return "INSTR_32BIT"

def test_extract_instruction_name():
    """Test the extract_instruction_name function logic"""
    
    hex_val = 0x2337b0104627b
    instruction_data = hex_val  # Simulate 128-bit with lower 64 bits
    length = "INSTR_64BIT"
    
    print(f"\nTesting extract_instruction_name for: 0x{hex_val:016x}")
    
    word1 = hex_val & 0xFFFFFFFF
    word2 = (hex_val >> 32) & 0xFFFFFFFF
    
    ace_op = word1 & 0x7F
    tuop = (word1 >> 12) & 0x7
    
    print(f"ACE_OP: {ace_op}")
    print(f"TUOP: {tuop}")
    
    if ace_op == 123:  # TILE_ACE_OP
        if tuop == 6:  # tuop_110
            print("Detected as tuop_110")
            
            # Check for different instruction types in tuop_110
            memuop = (word1 >> 25) & 0x3F
            lsuop = (word1 >> 10) & 0x3
            tuop_second = (word2 >> 12) & 0x7
            
            print(f"memuop: {memuop}")
            print(f"lsuop: {lsuop}")
            print(f"tuop_second: {tuop_second}")
            
            if length == "INSTR_64BIT" and memuop == 0:
                if tuop_second == 3:  # tuop_011
                    print("✓ Detected as tmma instruction (tuop_110 + tuop_011)")
                    
                    # Call extract_tmma_instruction_name
                    return extract_tmma_instruction_name_python(hex_val)
                else:
                    print(f"tuop_second is {tuop_second}, not 3")
                    return "tld_64bit_unimplemented"
            else:
                print(f"Not 64-bit or memuop != 0")
                return "tld_64bit_unimplemented"
        elif tuop == 1:  # tuop_001
            return "tmma.ttt"  # Simple case
    
    return "unknown"

def extract_tmma_instruction_name_python(instruction_data):
    """Python version of extract_tmma_instruction_name"""
    
    word1 = instruction_data & 0xFFFFFFFF
    word2 = (instruction_data >> 32) & 0xFFFFFFFF
    
    # Extract key fields
    memb = (word1 >> 7) & 0x1
    mop_5 = (word1 >> 9) & 0x1
    mop_4_0 = (word1 >> 26) & 0x1F
    mop_full = (mop_5 << 5) | mop_4_0
    reuse = (word2 >> 8) & 0x3
    ashape = (word2 >> 10) & 0x3
    mema = (word2 >> 31) & 0x1
    
    print(f"  memb: {memb}")
    print(f"  mema: {mema}")
    print(f"  mop_full: {mop_full}")
    print(f"  reuse: {reuse}")
    print(f"  ashape: {ashape}")
    
    # Determine instruction variant
    if mema == 0 and memb == 0:
        variant = "tmma.ttt"
    elif mema == 0 and memb == 1:
        variant = "tmma.ttr"
    elif mema == 1 and memb == 0:
        variant = "tmma.trt"
    else:
        variant = "tmma.trr"
    
    # Determine data type based on mop field
    data_types = {
        0: "tf32.tf32.f32",
        1: "f16.f16.f32",
        3: "bf16.bf16.f32",
        32: "f32.mxint8.mxint8"
    }
    data_type = data_types.get(mop_full, "unknown")
    
    # Determine reuse string
    reuse_strs = {
        0: "r32",
        1: "reused",
        2: "reused",
        3: "reused"
    }
    reuse_str = reuse_strs.get(reuse, "unknown")
    
    # Determine ashape string
    ashape_strs = {
        0: "m8",
        1: "m16",
        2: "m4",
        3: "m32"
    }
    ashape_str = ashape_strs.get(ashape, "unknown")
    
    # Build complete instruction name
    result = f"{variant}.{data_type}.{reuse_str}.{ashape_str}"
    print(f"  Generated: {result}")
    return result

def main():
    print("=== Testing SystemVerilog Logic ===")
    
    # Test instruction length detection
    length = test_systemverilog_logic()
    print(f"Detected length: {length}")
    
    # Test instruction name extraction
    instr_name = test_extract_instruction_name()
    print(f"Detected instruction: {instr_name}")
    
    print()
    print("=== Summary ===")
    if "tmma" in instr_name:
        print("✓ SUCCESS: Instruction correctly identified as tmma")
        print("✓ This should fix the 'tld_64bit_unimplemented' issue")
    else:
        print("✗ FAILED: Instruction not identified as tmma")

if __name__ == '__main__':
    main()
