#!/usr/bin/env python3

# 分析操作数字段
test_instr = 0x82216b000564fb
word2_test = (test_instr >> 32) & 0xFFFFFFFF

print(f'测试指令Word 2: 0x{word2_test:08x} = {word2_test:032b}')
print()

# 根据tmul.ttr.md提取字段
ace_op2 = word2_test & 0x7F  # bits [6:0]
bit7 = (word2_test >> 7) & 0x1  # bit 7
vecuop2_52 = (word2_test >> 8) & 0xF  # bits [11:8]
tuop2 = (word2_test >> 12) & 0x7  # bits [14:12]
ts1 = (word2_test >> 15) & 0xFF  # bits [22:15]
td = (word2_test >> 23) & 0xFF  # bits [30:23]
bit31 = (word2_test >> 31) & 0x1  # bit 31

print('字段提取:')
print(f'  ACE_op[6:0]: {ace_op2:07b} = 0x{ace_op2:02x} (期望 1111011)')
print(f'  bit[7]: {bit7} (期望 0)')
print(f'  vecuop2[5:2][11:8]: {vecuop2_52:04b} (期望 0001)')
print(f'  tuop[14:12]: {tuop2:03b} (期望 010)')
print(f'  Ts1[22:15]: {ts1:08b} = {ts1} (变量字段)')
print(f'  Td[30:23]: {td:08b} = {td} (变量字段)')
print(f'  bit[31]: {bit31} (期望 0)')

print()
print('匹配检查:')
print(f'  ACE_op: {"✓" if ace_op2 == 0b1111011 else "✗"}')
print(f'  bit[7]: {"✓" if bit7 == 0 else "✗"}')
print(f'  vecuop2[5:2]: {"✓" if vecuop2_52 == 0b0001 else "✗"}')
print(f'  tuop: {"✓" if tuop2 == 0b010 else "✗"}')
print(f'  bit[31]: {"✓" if bit31 == 0 else "✗"}')

print()
print('操作数解析:')
print(f'  Ts1 = T{ts1} (源操作数1)')
print(f'  Td = T{td} (目标操作数)')

# 检查Word 1的操作数
word1_test = test_instr & 0xFFFFFFFF
rs2 = (word1_test >> 15) & 0x1F  # bits [19:15]
print(f'  rs2 = x{rs2} (源操作数2)')

print()
print('完整指令应该是:')
print(f'  tmul.ttr.f32 T{td}, T{ts1}, x{rs2}')

# 检查是否匹配期望的 T1,T4,a0
if td == 1 and ts1 == 4 and rs2 == 10:
    print('✓ 操作数匹配期望的 T1,T4,a0 (a0=x10)')
else:
    print(f'✗ 操作数不匹配期望的 T1,T4,a0')
    print(f'  期望: T1,T4,x10')
    print(f'  实际: T{td},T{ts1},x{rs2}')
