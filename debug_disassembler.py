#!/usr/bin/env python3
"""
调试tile_disassembler.py的问题
"""

from tile_disassembler import TileDisassembler

def debug_instruction_matching():
    """调试指令匹配问题"""
    
    disasm = TileDisassembler()
    test_instruction = 0x82216b000564fb
    
    print(f"调试指令: 0x{test_instruction:016x}")
    print(f"二进制: {test_instruction:064b}")
    print()
    
    # 查找tmul.ttr指令定义
    tmul_ttr = None
    for name, instr in disasm.instructions.items():
        if name == 'tmul.ttr':
            tmul_ttr = instr
            break
    
    if not tmul_ttr:
        print("错误：找不到tmul.ttr指令定义")
        return
    
    print("tmul.ttr指令定义:")
    print(f"  总位数: {tmul_ttr.total_bits}")
    print(f"  匹配值: 0x{tmul_ttr.match_value:016x}")
    print(f"  匹配掩码: 0x{tmul_ttr.match_mask:016x}")
    print(f"  编码: {tmul_ttr.encoding}")
    print()
    
    print("字段详情:")
    for i, field in enumerate(tmul_ttr.fields):
        print(f"  {i}: {field.name}[{field.end_bit}:{field.start_bit}] = \"{field.attr}\" ({field.bits} bits)")
    print()
    
    # 检查匹配
    masked_instruction = test_instruction & tmul_ttr.match_mask
    print(f"指令 & 掩码: 0x{masked_instruction:016x}")
    print(f"匹配值:     0x{tmul_ttr.match_value:016x}")
    print(f"匹配结果: {'✓' if masked_instruction == tmul_ttr.match_value else '✗'}")
    print()
    
    # 分析不匹配的位
    if masked_instruction != tmul_ttr.match_value:
        diff = masked_instruction ^ tmul_ttr.match_value
        print(f"不匹配的位: 0x{diff:016x}")
        print(f"不匹配位置: {diff:064b}")
        
        # 找出哪些字段不匹配
        print("\n不匹配的字段:")
        for field in tmul_ttr.fields:
            if field.attr:  # 只检查固定字段
                field_mask = ((1 << field.bits) - 1) << field.start_bit
                field_value = (test_instruction >> field.start_bit) & ((1 << field.bits) - 1)
                expected_value = int(field.attr, 2) if all(c in '01' for c in field.attr) else None
                
                if expected_value is not None and field_value != expected_value:
                    print(f"  {field.name}[{field.end_bit}:{field.start_bit}]: 得到 {field_value:0{field.bits}b}, 期望 {field.attr}")
    
    # 测试64位匹配
    print(f"\n测试64位匹配:")
    result_64 = disasm.disassemble_instruction(test_instruction, 64)
    print(f"64位结果: {result_64}")
    
    # 测试32位匹配（只用低32位）
    print(f"\n测试32位匹配:")
    low32 = test_instruction & 0xFFFFFFFF
    result_32 = disasm.disassemble_instruction(low32, 32)
    print(f"32位结果: {result_32}")

def check_all_tmul_variants():
    """检查所有tmul变体的定义"""
    
    disasm = TileDisassembler()
    
    print("所有tmul指令变体:")
    for name, instr in disasm.instructions.items():
        if 'tmul' in name:
            print(f"\n{name}:")
            print(f"  总位数: {instr.total_bits}")
            print(f"  匹配值: 0x{instr.match_value:016x}")
            print(f"  匹配掩码: 0x{instr.match_mask:016x}")
            
            # 显示关键字段
            key_fields = ['ACE_op', 'vecuop1', 'tuop', 'vecuop2[5:2]']
            for field in instr.fields:
                if field.name in key_fields and field.attr:
                    print(f"    {field.name}[{field.end_bit}:{field.start_bit}] = {field.attr}")

def test_bit_field_extraction():
    """测试位字段提取是否正确"""
    
    test_instruction = 0x82216b000564fb
    print(f"测试指令: 0x{test_instruction:016x}")
    print(f"Word 1: 0x{test_instruction & 0xFFFFFFFF:08x}")
    print(f"Word 2: 0x{(test_instruction >> 32) & 0xFFFFFFFF:08x}")
    print()
    
    # 手动提取关键字段
    word1 = test_instruction & 0xFFFFFFFF
    word2 = (test_instruction >> 32) & 0xFFFFFFFF
    
    ace_op1 = word1 & 0x7F
    vecuop1 = (word1 >> 9) & 0x3  # 修正后的位置
    tuop1 = (word1 >> 12) & 0x7
    
    ace_op2 = word2 & 0x7F
    tuop2 = (word2 >> 12) & 0x7
    vecuop2_52 = (word2 >> 8) & 0xF
    
    print("手动提取的字段:")
    print(f"  ace_op1: {ace_op1:07b} = 0x{ace_op1:02x}")
    print(f"  vecuop1: {vecuop1:02b} = {vecuop1}")
    print(f"  tuop1: {tuop1:03b} = {tuop1}")
    print(f"  ace_op2: {ace_op2:07b} = 0x{ace_op2:02x}")
    print(f"  tuop2: {tuop2:03b} = {tuop2}")
    print(f"  vecuop2[5:2]: {vecuop2_52:04b} = {vecuop2_52}")
    print()
    
    # 检查是否符合tmul.ttr的期望
    print("与tmul.ttr期望值比较:")
    print(f"  ace_op1 = {ace_op1:07b}, 期望 1111011: {'✓' if ace_op1 == 0b1111011 else '✗'}")
    print(f"  vecuop1 = {vecuop1:02b}, 期望 10: {'✓' if vecuop1 == 0b10 else '✗'}")
    print(f"  tuop1 = {tuop1:03b}, 期望 110: {'✓' if tuop1 == 0b110 else '✗'}")
    print(f"  ace_op2 = {ace_op2:07b}, 期望 1111011: {'✓' if ace_op2 == 0b1111011 else '✗'}")
    print(f"  tuop2 = {tuop2:03b}, 期望 010: {'✓' if tuop2 == 0b010 else '✗'}")
    print(f"  vecuop2[5:2] = {vecuop2_52:04b}, 期望 0001: {'✓' if vecuop2_52 == 0b0001 else '✗'}")

if __name__ == "__main__":
    debug_instruction_matching()
    print("\n" + "="*60)
    check_all_tmul_variants()
    print("\n" + "="*60)
    test_bit_field_extraction()
