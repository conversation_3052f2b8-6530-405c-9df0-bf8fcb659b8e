```wavedrom
//tkernel_dims
{"reg": [
    {"bits": 4,  "name": 'tb_dim', "attr": 'RO'},
    {"bits": 12,  "name": 'tbc_dim_x', "attr": 'RO'},
    {"bits": 8,  "name": 'tbc_dim_y', "attr": 'RO'},
    {"bits": 8,  "name": 'tbc_dim_z', "attr": 'RO'},
    {"bits": 16,  "name": 'grid_dim_x', "attr": 'RO'},
    {"bits": 8,  "name": 'grid_dim_y', "attr": 'RO'},
    {"bits": 8,  "name": 'grid_dim_z', "attr": 'RO'},
], config: {"bits":64,lanes:2,hspace: 1500}}
```