```wavedrom
//tsync.i
{"reg": [
    {"bits": 7,  "name": 'ACE_op',    "attr": '1111011'},
    {"bits": 5,  "name": '00000',       "attr": ''},
    {"bits": 3,  "name": 'tuop',     "attr": '101'},
    {"bits": 5,  "name": 'sync_id',       "attr": ''},
    {"bits": 3,  "name": '',       "attr": '000'},
    {"bits": 3,  "name": 'ctrluop',    "attr": '000'},
    {"bits": 2,  "name": '',       "attr": '00'},
    {"bits": 1,  "name": 'scope',        "attr": ''},
    {"bits": 1,  "name": '',       "attr": '0'},
    {"bits": 1,  "name": 'blk', "attr":''},
    {"bits": 1,  "name": '',    "attr": '0'},
],config:{"bits":32,lanes:1,hspace: 1500}}
```
