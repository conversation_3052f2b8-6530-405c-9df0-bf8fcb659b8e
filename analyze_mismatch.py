#!/usr/bin/env python3

test_instr = 0x82216b000564fb
tmul_match = 0x217b000064fb
tmul_mask = 0x80007fff85f077ff

print(f'测试指令:     0x{test_instr:016x}')
print(f'tmul.ttr匹配: 0x{tmul_match:016x}')
print(f'tmul.ttr掩码: 0x{tmul_mask:016x}')
print()

masked_test = test_instr & tmul_mask
print(f'测试指令&掩码: 0x{masked_test:016x}')
print(f'匹配结果: {masked_test == tmul_match}')

if masked_test != tmul_match:
    diff = masked_test ^ tmul_match
    print(f'差异位: 0x{diff:016x}')
    print(f'差异二进制: {diff:064b}')
    
    # 分析具体哪些位不同
    print('\n位级分析:')
    for i in range(64):
        if (diff >> i) & 1:
            print(f'  位 {i} 不匹配')
    
    # 分析字段级差异
    print('\n字段分析:')
    word1_test = test_instr & 0xFFFFFFFF
    word1_match = tmul_match & 0xFFFFFFFF
    word2_test = (test_instr >> 32) & 0xFFFFFFFF
    word2_match = (tmul_match >> 32) & 0xFFFFFFFF
    
    print(f'Word 1 - 测试: 0x{word1_test:08x}, 匹配: 0x{word1_match:08x}')
    print(f'Word 2 - 测试: 0x{word2_test:08x}, 匹配: 0x{word2_match:08x}')
    
    if word1_test != word1_match:
        print('Word 1 不匹配')
    if word2_test != word2_match:
        print('Word 2 不匹配')
