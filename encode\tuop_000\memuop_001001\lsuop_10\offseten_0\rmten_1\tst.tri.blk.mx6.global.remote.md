```wavedrom
//tst.trr.blk.mx6.global.remote
{"reg": [
    {"bits": 7,  "name": 'ACE_op',    "attr": '1111011'},
    {"bits": 2,  "name": '',    "attr": '00'},
    {"bits": 1,  "name": '',        "attr": '0'},
    {"bits": 2,  "name": 'lsuop',    "attr": '10'},
    {"bits": 3,  "name": 'tuop',     "attr": '110'},
    {"bits": 2,  "name": '',       "attr": '00'},
    {"bits": 2,  "name": '',       "attr": '00'},
    {"bits": 1,  "name": '',       "attr": '0'},
    {"bits": 5,  "name": 'imm2',       "attr": ''},
    {"bits": 6,  "name": 'memuop', "attr":'001001'},
    {"bits": 1,  "name": '',    "attr": '0'},
    {"bits": 7,  "name": 'ACE_op',    "attr": '1111011'},
    {"bits": 2,  "name": '',        "attr": '00'},
    {"bits": 1,  "name": 'r_rsp',        "attr": ''},
    {"bits": 1,  "name": 'order',        "attr": ''},
    {"bits": 1,  "name": 'rmt',        "attr": '1'},
    {"bits": 3,  "name": 'tuop',     "attr": '000'},
    {"bits": 5,  "name": 'rs1',       "attr": ''},
    {"bits": 3,  "name": 'tilesize',       "attr": ''},
    {"bits": 8,  "name": 'Ts1',       "attr": ''},
    {"bits": 1,  "name": '',     "attr": '1'},
],config:{"bits":64,lanes:2,hspace: 1500}}
```
