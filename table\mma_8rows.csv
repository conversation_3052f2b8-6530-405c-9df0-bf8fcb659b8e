Input Shape Mode,Ext_direct,output bits,micro_num,A Shape(MxK),<PERSON> <PERSON>hape(NxK),<PERSON> Shape(MxN),Comp<PERSON> Shape(MxNxK),A TileRegNum(MxK),B TileRegNum(NxK),<PERSON> TileRegNum(MxN)
normal,No-extend,32 bit,0,8x128B,32x128B,8x32x32bit,8x32x128B,1(1x1),4(4x1),1(1x1)
normal,K-extend,32 bit,1,8x256B,32x256B,8x32x32bit,8x32x256B,2(1x2),8(4x2),1(1x1)
normal,K-extend,32 bit,2,8x512B,32x512B,8x32x32bit,8x32x512B,4(1x4),16(4x4),1(1x1)
A8W4,No-extend,32 bit,0,8x256B,32x128B,8x32x32bit,8x32x(256B_128B),2(1x2),4(4x1),1(1x1)
A8W4,K-extend,32 bit,1,8x512B,32x256B,8x32x32bit,8x32x(512B_256B),4(1x4),8(4x2),1(1x1)
A8W4,K-extend,32 bit,2,8x1024B,32x512B,8x32x32bit,8x32x(1024B_512B),8(1x8),16(4x4),1(1x1)
MXFP6,No-extend,32 bit,0,8x384B,32x384B,8x32x32bit,8x32x384B,3(1x3),12(4x3),1(1x1)
MXFP6,K-extend,32 bit,1,8x768B,32x768B,8x32x32bit,8x32x768B,6(1x6),24(4x6),1(1x1)
MXFP6,K-extend,32 bit,2,8x1536B,32x1536B,8x32x32bit,8x32x1536B,12(1x12),48(4x12),1(1x1)
