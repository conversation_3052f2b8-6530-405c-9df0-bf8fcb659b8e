```wavedrom
//tmma.ttr
{"reg": [
    {"bits": 7,  "name": 'ACE_op',    "attr": '1111011'},
    {"bits": 1,  "name": 'memb',    "attr": '1'},
    {"bits": 1,  "name": '',    "attr": '0'},
    {"bits": 1,  "name": 'mop[5]',    "attr": ''},
    {"bits": 1,  "name": 'noacc',    "attr": ''},
    {"bits": 1,  "name": 'neg',    "attr": ''},
    {"bits": 3,  "name": 'tuop',     "attr": '110'},
    {"bits": 5,  "name": 'rs2', "attr":''},
    {"bits": 3,  "name": '',     "attr": '000'},
    {"bits": 2,  "name": '',       "attr": '00'},
    {"bits": 1,  "name": 'gemv',       "attr": '0'},
    {"bits": 5,  "name": 'mop[4:0]',       "attr": ''},
    {"bits": 1,  "name": '',    "attr": '0'},
    {"bits": 7,  "name": 'ACE_op',    "attr": '1111011'},
    {"bits": 1,  "name": '',        "attr": '0'},
    {"bits": 2,  "name": 'reuse',    "attr": ''},
    {"bits": 2,  "name": 'ashape',    "attr": ''},
    {"bits": 3,  "name": 'tuop',     "attr": '011'},
    {"bits": 8,  "name": 'Ts1',       "attr": ''},
    {"bits": 8,  "name": 'Td',       "attr": ''},
    {"bits": 1,  "name": 'mema',     "attr": '0'},
],config:{"bits":64,lanes:2,hspace: 1500}}
```