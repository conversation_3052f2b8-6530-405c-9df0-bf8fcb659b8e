```wavedrom
//tsynccounter
{"reg": [
    {"bits":8,  "name": 'smem_ld_cnt', "attr": 'RO'},
    {"bits":8,  "name": 'smem_st_cnt', "attr": 'RO'},
    {"bits":8,  "name": 'gmem_ld_cnt', "attr": 'RO'},
    {"bits":8,  "name": 'gmem_st_cnt', "attr": 'RO'},
    {"bits":8,  "name": 'commit_group', "attr": 'RO'},
    {"bits":8,  "name": 'remote_fence_cnt', "attr": 'RO'},
    {"bits":14,  "name": '', "attr": 'RO'},
    {"bits":1,  "name": 'mem', "attr": 'RO'},
    {"bits":1,  "name": 'all', "attr": 'RO'},
], config: {"bits":64,lanes:2,hspace: 1500}}
```