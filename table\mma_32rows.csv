Input Shape Mode,Ext_direct,output bits,micro_num,A Shape(MxK),B <PERSON>hape(NxK),<PERSON> Shape(MxN),Comp<PERSON> Shape(MxNxK),A TileRegNum(MxK),B TileRegNum(NxK),<PERSON> TileRegNum(MxN)
normal,No-extend,32 bit,0,32x32B,32x32B,32x32x32bit,32x32x32B,1(1x1),1(1x1),4(1x4)
normal,K-extend,32 bit,1,32x64B,32x64B,32x32x32bit,32x32x64B,2(1x2),2(1x2),4(1x4)
normal,K-extend,32 bit,2,32x128B,32x128B,32x32x32bit,32x32x128B,4(1x4),4(1x4),4(1x4)
A8W4,No-extend,32 bit,0,32x64B,32x32B,32x32x32bit,32x32x(64B_32B),2(1x2),1(1x1),4(1x4)
A8W4,K-extend,32 bit,1,32x128B,32x64B,32x32x32bit,32x32x(128B_64B),4(1x4),2(1x2),4(1x4)
A8W4,K-extend,32 bit,2,32x256B,32x128B,32x32x32bit,32x32x(256B_128B),8(1x8),4(1x4),4(1x4)
MXFP6,No-extend,32 bit,0,32x96B,32x96B,32x32x32bit,32x32x96B,3(1x3),3(1x3),4(1x4)
MXFP6,K-extend,32 bit,1,32x192B,32x192B,32x32x32bit,32x32x192B,6(1x6),6(1x6),4(1x4)
MXFP6,K-extend,32 bit,2,32x384B,32x384B,32x32x32bit,32x32x384B,12(1x12),12(1x12),4(1x4)
