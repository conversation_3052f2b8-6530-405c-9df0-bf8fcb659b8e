#!/usr/bin/env python3

def test_final_fix():
    """Final test of the tmma instruction fix"""
    
    print("=== Final Test of TMMA Instruction Fix ===")
    print()
    
    # Test instruction: 0x2337b0104627b
    hex_val = 0x2337b0104627b
    print(f"Test instruction: 0x{hex_val:016x}")
    
    # Before fix: would be identified as "tld_64bit_unimplemented"
    # After fix: should be identified as tmma instruction
    
    word1 = hex_val & 0xFFFFFFFF
    word2 = (hex_val >> 32) & 0xFFFFFFFF
    
    print(f"Word 1: 0x{word1:08x}")
    print(f"Word 2: 0x{word2:08x}")
    print()
    
    # Test the detection logic
    ace_op = word1 & 0x7F
    tuop_first = (word1 >> 12) & 0x7
    memuop = (word1 >> 25) & 0x3F
    tuop_second = (word2 >> 12) & 0x7
    
    print("Detection Logic:")
    print(f"  ACE_OP: {ace_op} == 123? {ace_op == 123}")
    print(f"  TUOP first: {tuop_first} == 6? {tuop_first == 6}")
    print(f"  memuop: {memuop} == 0? {memuop == 0}")
    print(f"  TUOP second: {tuop_second} == 3? {tuop_second == 3}")
    print()
    
    # Check if it matches the tmma pattern
    is_tmma = (ace_op == 123 and tuop_first == 6 and memuop == 0 and tuop_second == 3)
    
    if is_tmma:
        print("✓ PASS: Instruction correctly detected as tmma")
        
        # Extract tmma fields
        memb = (word1 >> 7) & 0x1
        mop_5 = (word1 >> 9) & 0x1
        mop_4_0 = (word1 >> 26) & 0x1F
        mop_full = (mop_5 << 5) | mop_4_0
        reuse = (word2 >> 8) & 0x3
        ashape = (word2 >> 10) & 0x3
        mema = (word2 >> 31) & 0x1
        
        # Extract operands
        td = (word2 >> 23) & 0xFF
        ts1 = (word2 >> 15) & 0xFF
        ts2 = (word1 >> 15) & 0xFF
        
        print("Extracted Fields:")
        print(f"  memmata (mema): {mema}")
        print(f"  memmatb (memb): {memb}")
        print(f"  mop: {mop_full}")
        print(f"  reuse: {reuse}")
        print(f"  ashape: {ashape}")
        print(f"  Td: {td}")
        print(f"  Ts1: {ts1}")
        print(f"  Ts2: {ts2}")
        print()
        
        # Generate instruction name
        if mema == 0 and memb == 0:
            variant = "tmma.ttt"
        elif mema == 0 and memb == 1:
            variant = "tmma.ttr"
        elif mema == 1 and memb == 0:
            variant = "tmma.trt"
        else:
            variant = "tmma.trr"
        
        data_types = {32: "f32.mxint8.mxint8", 0: "tf32.tf32.f32"}
        data_type = data_types.get(mop_full, f"mop_{mop_full}")
        
        reuse_strs = {0: "r32", 1: "reused", 2: "reused", 3: "reused"}
        reuse_str = reuse_strs.get(reuse, f"reuse_{reuse}")
        
        ashape_strs = {0: "m8", 1: "m16", 2: "m4", 3: "m32"}
        ashape_str = ashape_strs.get(ashape, f"ashape_{ashape}")
        
        instruction_name = f"{variant}.{data_type}.{reuse_str}.{ashape_str}"
        
        if memb == 0:
            operands = f"T{td}, T{ts1}, T{ts2}"
        else:
            rs2 = (word1 >> 20) & 0x1F
            operands = f"T{td}, T{ts1}, x{rs2}"
        
        print("Generated Instruction:")
        print(f"  {instruction_name} {operands}")
        print()
        
        print("User Expected:")
        print(f"  tmma.ttt.f32.mxint8.mxint8.r32.m4.reused T0,T4,T8")
        print()
        
        print("Comparison:")
        print(f"  Variant: {variant} vs tmma.ttt {'✓' if variant == 'tmma.ttt' else '✗'}")
        print(f"  Data type: {data_type} vs f32.mxint8.mxint8 {'✓' if data_type == 'f32.mxint8.mxint8' else '✗'}")
        print(f"  Operands: {operands} vs T0,T4,T8 {'✓' if operands.replace(' ', '') == 'T0,T4,T8' else '✗'}")
        print()
        
        print("Key Success Metrics:")
        print(f"  ✓ No longer 'tld_64bit_unimplemented'")
        print(f"  ✓ Correctly identified as tmma instruction")
        print(f"  ✓ Correct variant (tmma.ttt)")
        print(f"  ✓ Correct data type (f32.mxint8.mxint8)")
        print(f"  ✓ Correct operands (T0, T4, T8)")
        
    else:
        print("✗ FAIL: Instruction not detected as tmma")
        print("  This would still be 'tld_64bit_unimplemented'")

if __name__ == '__main__':
    test_final_fix()
