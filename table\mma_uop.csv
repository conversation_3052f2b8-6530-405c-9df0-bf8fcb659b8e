Index,Data-type,Multiplicands (A),Multiplicands (B),Accumulator (D),Compute Mode,Level
0,Alternate floating Point,.tf32,.tf32,.f32,Normal,P0
1,Floating Point,.f16,.f16,.f32,Normal,P0
3,Alternate floating Point,.bf16,.bf16,.f32,Normal,P0
5,Alternate floating Point,.fp8(.e4m3),.fp8(.e4m3),.f32,Normal,P0
7,Alternate floating Point,.fp8(.e5m2),.fp8(e5m2),.f32,Normal,P0
9,Alternate floating Point,.fp8(.e4m3),.fp8(e5m2),.f32,Normal,P0
11,Alternate floating Point,.fp8(.e5m2),.fp8(.e4m3),.f32,Normal,P0
13,Integer,.u8,.u8,.s32,Normal,P0
14,Integer,.s8,.s8,.s32,Normal,P0
15,4-bit integer,.u4,.u4,.s32,Normal,P0
16,4-bit integer,.s4,.s4,.s32,Normal,P0
17,8/4-bit integer,.u8,.u4,.s32,A8W4,P0
18,8/4-bit integer,.s8,.s4,.s32,A8W4,P0
,,,,,,
,,,,,,
32,MXINT8,MXINT8,MXINT8,.f32,Normal,P0
34,MXINT4,MXINT4,MXINT4,.f32,Normal,P1
36,MXINT8/4,MXINT8,MXINT4,.f32,A8W4,P1
38,MXFP8,MXFP8(.e4m3),MXFP8(.e4m3),.f32,Normal,P1
40,MXFP8,MXFP8(.e5m2),MXFP8(.e5m2),.f32,Normal,P1
42,MXFP8,MXFP8(.e4m3),MXFP8(.e5m2),.f32,Normal,P1
44,MXFP8,MXFP8(.e5m2),MXFP8(.e4m3),.f32,Normal,P1
46,MXFP6,MXFP6(.e3m2),MXFP6(.e3m2),.f32,A6W6,P0
48,MXFP6,MXFP6(.e2m3),MXFP6(.e2m3),.f32,A6W6,P0
50,MXFP4,MXFP4(e2m1),MXFP4(e2m1),.f32,Normal,P0
