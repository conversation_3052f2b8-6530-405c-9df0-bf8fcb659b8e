#!/usr/bin/env python3

from tile_disassembler import TileDisassembler

def test_tmul_ttr():
    d = TileDisassembler()
    
    # 检查tmul.ttr的新匹配值
    if 'tmul.ttr' in d.instructions:
        instr = d.instructions['tmul.ttr']
        print(f'tmul.ttr: mask=0x{instr.match_mask:016x}, value=0x{instr.match_value:016x}')
        print(f'编码: {instr.encoding}')
        
        test_instr = 0x82216b000564fb
        masked = test_instr & instr.match_mask
        print(f'测试指令: 0x{test_instr:016x}')
        print(f'掩码后: 0x{masked:016x}')
        print(f'匹配: {masked == instr.match_value}')
        
        if masked == instr.match_value:
            result = d.disassemble_instruction(test_instr, 64)
            print(f'反汇编结果: {result}')
        else:
            diff = masked ^ instr.match_value
            print(f'差异: 0x{diff:016x}')
    else:
        print('找不到tmul.ttr指令')

if __name__ == "__main__":
    test_tmul_ttr()
