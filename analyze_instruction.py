#!/usr/bin/env python3

def analyze_instruction_0x2337b0104627b():
    """Analyze the instruction 0x2337b0104627b"""
    
    hex_val = 0x2337b0104627b
    print(f'Analyzing instruction: 0x{hex_val:016x}')
    print(f'Binary: {hex_val:064b}')
    print()

    # Split into two 32-bit words
    word1 = hex_val & 0xFFFFFFFF
    word2 = (hex_val >> 32) & 0xFFFFFFFF
    print(f'Word 1 (low):  0x{word1:08x} = {word1:032b}')
    print(f'Word 2 (high): 0x{word2:08x} = {word2:032b}')
    print()

    # Analyze first word fields
    ace_op = word1 & 0x7F
    tuop = (word1 >> 12) & 0x7
    print(f'ACE_OP: {ace_op:07b} = {ace_op} (should be 123 = 0x7B for tile instructions)')
    print(f'TUOP: {tuop:03b} = {tuop}')

    # Check if this is a tile instruction
    if ace_op == 123:
        print('✓ This is a tile instruction')
        
        if tuop == 6:  # tuop_110
            print('✓ This is tuop_110 - multi-lane memory/matrix instructions')
            
            # Extract memuop field from first word
            memuop = (word1 >> 25) & 0x3F
            print(f'memuop: {memuop:06b} = {memuop}')
            
            # Extract lsuop field from first word  
            lsuop = (word1 >> 10) & 0x3
            print(f'lsuop: {lsuop:02b} = {lsuop}')
            
            # Extract tuop from second word
            tuop_second = (word2 >> 12) & 0x7
            print(f'tuop_second: {tuop_second:03b} = {tuop_second}')
            
            # Check for tmma pattern: tuop_110 + tuop_011
            if tuop_second == 3:  # tuop_011
                print('✓ This is a tmma instruction (tuop_110 + tuop_011)')
                
                # Extract memmata from second word bit 31
                memmata = (word2 >> 31) & 0x1
                print(f'memmata: {memmata}')
                
                # Extract memmatb from first word bit 8
                memmatb = (word1 >> 8) & 0x1
                print(f'memmatb: {memmatb}')
                
                # Extract mop field - need to be careful about bit positions
                # According to wavedrom: mop[5] is at bit 10, mop[4:0] is at bits 30:25 in first word
                mop_5 = (word1 >> 10) & 0x1
                mop_4_0 = (word1 >> 25) & 0x1F
                mop_full = (mop_5 << 5) | mop_4_0
                print(f'mop[5]: {mop_5}')
                print(f'mop[4:0]: {mop_4_0:05b} = {mop_4_0}')
                print(f'Full mop: {mop_full:06b} = {mop_full}')

                # Let's also check the actual bit extraction more carefully
                print(f'Word1 bits 30:25: {(word1 >> 25) & 0x3F:06b}')
                print(f'Word1 bit 10: {(word1 >> 10) & 0x1}')
                
                # Extract reuse field from second word
                reuse = (word2 >> 9) & 0x3
                print(f'reuse: {reuse:02b} = {reuse}')
                
                # Extract ashape field from second word
                ashape = (word2 >> 11) & 0x3
                print(f'ashape: {ashape:02b} = {ashape}')
                
                # Extract operand fields - let me check the wavedrom more carefully
                # According to tmma.ttt.md: Td is at bits 23:16 in second word, Ts1 is at bits 15:8, Ts2 is at bits 23:16 in first word
                td = (word2 >> 16) & 0xFF
                ts1 = (word2 >> 8) & 0xFF  # Ts1 field from second word
                ts2 = (word1 >> 16) & 0xFF  # Ts2 field from first word

                print(f'Td: {td:08b} = T{td}')
                print(f'Ts1: {ts1:08b} = T{ts1}')
                print(f'Ts2: {ts2:08b} = T{ts2}')

                # Extract rs1 field from second word (for ttr variant)
                rs1 = (word2 >> 13) & 0x1F
                print(f'rs1: {rs1:05b} = x{rs1}')

                # Let me also check if there are other interpretations
                print(f'Alternative Td extraction from bits 23:16 in word2: T{(word2 >> 16) & 0xFF}')
                print(f'Alternative Ts1 extraction from bits 15:8 in word2: T{(word2 >> 8) & 0xFF}')
                
                print()
                print('Instruction variant analysis:')
                if memmata == 0 and memmatb == 0:
                    variant = 'tmma.ttt'
                elif memmata == 0 and memmatb == 1:
                    variant = 'tmma.ttr'
                elif memmata == 1 and memmatb == 0:
                    variant = 'tmma.trt'
                elif memmata == 1 and memmatb == 1:
                    variant = 'tmma.trr'
                
                print(f'Variant: {variant}')
                
                # Determine data types based on mop field (from mma_uop.csv)
                # But the user expects f32.mxint8.mxint8, so let's check if we need to look at other fields
                data_type = 'unknown'
                if mop_full == 0:
                    data_type = 'tf32.tf32.f32'
                elif mop_full == 1:
                    data_type = 'f16.f16.f32'
                elif mop_full == 3:
                    data_type = 'bf16.bf16.f32'
                elif mop_full == 32:
                    data_type = 'mxint8.mxint8.f32'
                elif mop_full == 39:  # This is what the user mentioned
                    data_type = 'f32.mxint8.mxint8'  # This doesn't match the table
                else:
                    data_type = f'unknown_mop_{mop_full}'

                # The user expects f32.mxint8.mxint8 but we got mop=0 which is tf32.tf32.f32
                # Let's check if there are other fields that determine the data type
                print(f'Note: User expects f32.mxint8.mxint8 but mop=0 suggests tf32.tf32.f32')
                
                print(f'Data type: {data_type}')
                
                # Check reuse field - need to research the exact mapping
                reuse_str = 'unknown'
                if reuse == 0b00:
                    reuse_str = 'r32'
                elif reuse == 0b01:
                    reuse_str = 'reused'  # Based on user's expected output
                elif reuse == 0b10:
                    reuse_str = 'reused'
                
                print(f'Reuse: {reuse_str}')
                
                # Check ashape field  
                ashape_str = 'unknown'
                if ashape == 0b00:
                    ashape_str = 'm8'
                elif ashape == 0b01:
                    ashape_str = 'm16'
                elif ashape == 0b10:
                    ashape_str = 'm4'
                elif ashape == 0b11:
                    ashape_str = 'm32'
                
                print(f'A shape: {ashape_str}')
                
                print()
                print('Correct instruction should be:')
                if variant == 'tmma.ttt':
                    print(f'tmma.ttt.{data_type}.{reuse_str}.{ashape_str}.{reuse_str} T{td},T{ts1},T{ts2}')
                    print(f'User expects: tmma.ttt.f32.mxint8.mxint8.r32.m4.reused T0,T4,T8')
                elif variant == 'tmma.ttr':
                    print(f'tmma.ttr.{data_type}.{reuse_str}.{ashape_str}.{reuse_str} T{td},T{ts1},x{rs1}')

                # Let me try a different interpretation - maybe the mop field is encoded differently
                print()
                print('Checking if mop field should be interpreted differently...')
                # Maybe mop=32 for mxint8.mxint8.f32 requires different bit extraction
                # Let me check if there are other fields that determine the data type
                
            else:
                print(f'Second word tuop is {tuop_second}, not matrix operations')
                
        elif tuop == 3:  # tuop_011
            print('This is tuop_011 - matrix operations')
        else:
            print(f'TUOP {tuop} - other operations')
    else:
        print('This is not a tile instruction')

if __name__ == '__main__':
    analyze_instruction_0x2337b0104627b()
