```wavedrom
//twait.r.rmtfence
{"reg": [
    {"bits": 7,  "name": 'ACE_op',    "attr": '1111011'},
    {"bits": 5,  "name": '', "attr":'00000'},
    {"bits": 3,  "name": 'tuop',     "attr": '101'},
    {"bits": 5,  "name": 'rs1',       "attr": ''},
    {"bits": 3,  "name": '',       "attr": ''},
    {"bits": 3,  "name": 'ctrluop',    "attr": '001'},
    {"bits": 2,  "name": '',        "attr": '00'},
    {"bits": 3,  "name": 'waitop',    "attr": '011'},
    {"bits": 1,  "name": '',    "attr": '0'},
],config:{"bits":32,lanes:1,hspace: 1500}}
```