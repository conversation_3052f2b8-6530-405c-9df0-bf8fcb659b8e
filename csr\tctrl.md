```wavedrom
//tctrl
{"reg": [
    {"bits": 3,  "name": 'tfrnd', "attr": 'RW'},
    {"bits": 2,  "name": 'tirnd', "attr": 'RW'},
    {"bits": 3,  "name": 'tsat', "attr": 'RW'},
    {"bits": 3,  "name": 'tsatfinite', "attr": 'RW'},
    {"bits": 3,  "name": 'trelu', "attr": 'RW'},
    {"bits": 3,  "name": 'tnan', "attr": 'RW'},
    {"bits": 1,  "name": 'tnocp', "attr": 'RW'},
    {"bits": 14,  "name": '', "attr": 'RO'},
    {"bits": 1,  "name": 'tmsatfinite', "attr": 'RW'},
    {"bits": 31,  "name": '', "attr": 'RO'},
], config: {"bits":64,lanes:2,hspace: 1500}}
```