#!/usr/bin/env python3

from tile_disassembler import TileDisassembler

def debug_64bit_matching():
    d = TileDisassembler()
    test_instr = 0x82216b000564fb
    
    print(f'测试指令: 0x{test_instr:016x}')
    print()
    
    # 检查所有64位指令
    count_64bit = 0
    matches = []
    
    for name, instr in d.instructions.items():
        if instr.total_bits == 64:
            count_64bit += 1
            masked = test_instr & instr.match_mask
            if masked == instr.match_value:
                matches.append(name)
                print(f'匹配到: {name}')
    
    print(f'总共有 {count_64bit} 个64位指令')
    print(f'匹配到 {len(matches)} 个指令')
    
    if matches:
        # 显示第一个匹配的指令详情
        first_match = d.instructions[matches[0]]
        result = d._format_instruction(test_instr, first_match)
        print(f'反汇编结果: {result}')
    else:
        print('没有匹配的指令')
        
        # 检查tmul.ttr的详细信息
        if 'tmul.ttr' in d.instructions:
            tmul = d.instructions['tmul.ttr']
            print(f'\ntmul.ttr详情:')
            print(f'  匹配值: 0x{tmul.match_value:016x}')
            print(f'  匹配掩码: 0x{tmul.match_mask:016x}')
            
            masked = test_instr & tmul.match_mask
            print(f'  测试指令掩码后: 0x{masked:016x}')
            print(f'  差异: 0x{(masked ^ tmul.match_value):016x}')
            
            # 分析字段级差异
            print(f'\n字段分析:')
            for field in tmul.fields:
                if field.attr and field.name:  # 固定字段
                    field_mask = (1 << field.bits) - 1
                    actual = (test_instr >> field.start_bit) & field_mask
                    expected = int(field.attr, 2) if all(c in '01' for c in field.attr) else 0
                    match = actual == expected
                    print(f'    {field.name}[{field.end_bit}:{field.start_bit}]: 实际 {actual:0{field.bits}b}, 期望 {field.attr} {"✓" if match else "✗"}')

if __name__ == "__main__":
    debug_64bit_matching()
