```wavedrom
//twait.i.ls
{"reg": [
    {"bits": 7,  "name": 'ACE_op',    "attr": '1111011'},
    {"bits": 5,  "name": '',    "attr": '00000'},
    {"bits": 3,  "name": 'tuop',     "attr": '101'},
    {"bits": 8,  "name": 'cnt',       "attr": ''},
    {"bits": 3,  "name": 'ctrluop',    "attr": '001'},
    {"bits": 1,  "name": 'isShare', "attr":''},
    {"bits": 1,  "name": 'isStore', "attr": ''},
    {"bits": 3,  "name": 'waitop',    "attr": '000'},
    {"bits": 1,  "name": '',    "attr": '0'},
],config:{"bits":32,lanes:1,hspace: 1500}}
```