// Tile Extension ISA Instruction Decoder
// SystemVerilog implementation for instruction length detection, 
// tile instruction identification, and disassembly

package tile_decoder_pkg;

    // Instruction length enumeration
    typedef enum logic [1:0] {
        INSTR_32BIT  = 2'b00,
        INSTR_64BIT  = 2'b01,
        INSTR_96BIT  = 2'b10,
        INSTR_128BIT = 2'b11
    } instr_length_e;

    // ACE_OP constant for tile instructions
    parameter logic [6:0] TILE_ACE_OP = 7'b1111011;

    // Instruction collection state
    typedef struct packed {
        logic [127:0] instruction_data;
        logic [2:0]   collected_words;  // Changed from [1:0] to [2:0] to support up to 4 words (128-bit instructions)
        instr_length_e expected_length;
        logic         is_complete;
        logic         is_tile_instr;
    } instr_collector_t;

    // Function to extract ACE_OP from 32-bit word
    function automatic logic [6:0] extract_ace_op(input logic [31:0] word);
        return word[6:0];
    endfunction

    // Function to check if instruction is a tile instruction
    function automatic logic is_tile_instruction(input logic [31:0] first_word);
        logic [6:0] ace_op;
        ace_op = extract_ace_op(first_word);
        return (ace_op == TILE_ACE_OP);
    endfunction

    // Function to check if instruction is a sync or wait instruction
    function automatic logic is_sync_or_wait_instruction(input logic [31:0] first_word);
        logic [6:0] ace_op;
        logic [2:0] tuop;
        logic [2:0] ctrluop;
        logic [2:0] waitop;
        logic [2:0] miscop;
        logic ace_misc_en;

        ace_op = extract_ace_op(first_word);

        // Check if this is a tile instruction
        if (ace_op != TILE_ACE_OP) begin
            return 1'b0; // Not a tile instruction
        end

        tuop = first_word[14:12];

        case (tuop)
            3'b100: begin // tuop_100 - CSR operations
                // All CSR operations (tcsrr, tcsrw) should not go to dispatch stage
                return 1'b1; // This is a CSR instruction
            end

            3'b101: begin // tuop_101 - sync operations
                ctrluop = first_word[25:23];
                waitop = first_word[30:28];

                case (ctrluop)
                    3'b000: return 1'b1; // tsync operations
                    3'b001: return 1'b1; // all twait operations (waitop determines specific type)
                    3'b010: return 1'b1; // trmtfence operations
                    default: return 1'b0;
                endcase
            end

            3'b111: begin // tuop_111 - ACE operations
                miscop = first_word[28:26];
                ace_misc_en = first_word[31];

                if (ace_misc_en == 1'b1) begin
                    case (miscop)
                        3'b000: return 1'b1;  // ace_bsync
                        3'b010: return 1'b1;  // ace_nbsync
                        default: return 1'b0;
                    endcase
                end else begin
                    return 1'b0;
                end
            end

            default: return 1'b0; // Not a sync/wait instruction
        endcase
    endfunction

    // Function to determine instruction length based on first 32-bit word
    function automatic instr_length_e get_instruction_length(input logic [31:0] first_word);
        logic [6:0] ace_op;
        logic [2:0] tuop;
        logic [5:0] memuop;
        logic [1:0] lsuop;

        ace_op = extract_ace_op(first_word);

        // Check if this is a tile instruction
        if (ace_op != TILE_ACE_OP) begin
            return INSTR_32BIT; // Non-tile instruction, assume 32-bit
        end

        // Extract tuop field (bits [14:12])
        tuop = first_word[14:12];

        case (tuop)
            3'b000: begin // tuop_000 - memory operations
                memuop = first_word[30:25]; // Extract memuop field (bits 30:25)
                lsuop = first_word[11:10];  // Extract lsuop field

                case (lsuop)
                    2'b00: return INSTR_64BIT;  // Linear/stride operations
                    2'b01: return INSTR_64BIT;  // Other 64-bit operations
                    2'b10: return INSTR_96BIT;  // Index operations (3-lane)
                    2'b11: begin
                        // Check memuop for 128-bit operations
                        if (memuop == 6'b011100) // tacp operations
                            return INSTR_128BIT;
                        else
                            return INSTR_64BIT;
                    end
                endcase
            end

            3'b001: return INSTR_64BIT;  // tuop_001 - matrix operations
            3'b010: begin // tuop_010 - vector operations
                logic [1:0] vecuop1 = first_word[10:9];
                if (vecuop1 == 2'b10) begin
                    // 2-operand ALU operations like tadd, tmul
                    return INSTR_64BIT;
                end else if (vecuop1 == 2'b11) begin
                    // 3-operand FMA operations like tfma (96-bit)
                    return INSTR_96BIT;
                end else begin
                    // Other vector operations (SFU, conversion)
                    return INSTR_64BIT;
                end
            end
            3'b011: return INSTR_64BIT;  // tuop_011 - other operations
            3'b100: return INSTR_32BIT;  // tuop_100 - CSR operations
            3'b101: return INSTR_32BIT;  // tuop_101 - sync operations
            3'b110: begin // tuop_110 - can be part of multi-lane instructions
                // First check if this might be a special vector ALU instruction with vecuop1=01
                logic [1:0] vecuop1 = first_word[10:9];
                logic [5:0] memuop_field = first_word[30:25];
                
                // Special case: vector ALU operations with vecuop1=01 but ADD/MUL encoding
                // These are 64-bit instructions like tadd.ttr.f32.neg2
                if (vecuop1 == 2'b01 && memuop_field == 6'b000000) begin
                    return INSTR_64BIT;
                end
                
                lsuop = first_word[11:10];  // Extract lsuop field
                memuop = first_word[30:25]; // Extract memuop field (bits 30:25)

                // Check for different instruction types
                if (memuop == 6'b000001) begin
                    // Block memory operations (dual-lane 64-bit)
                    return INSTR_64BIT;
                end else if (memuop == 6'b010001) begin
                    // tacp.commit_group operations (64-bit)
                    return INSTR_64BIT;
                end else if (memuop == 6'b000000) begin
                    // Standard memory operations
                    case (lsuop)
                        2'b00: return INSTR_64BIT;  // Linear operations (dual-lane)
                        2'b01: return INSTR_64BIT;  // Stride operations (dual-lane)
                        2'b10: return INSTR_96BIT;  // Index operations (3-lane)
                        2'b11: return INSTR_64BIT;  // Other 64-bit operations
                    endcase
                end else if (memuop == 6'b011100) begin
                    // Tile copy operations - check lsuop for actual length
                    case (lsuop)
                        2'b00: return INSTR_64BIT;   // Linear operations (tacp.rrr.linear)
                        2'b01: return INSTR_64BIT;   // Stride operations
                        2'b10: return INSTR_96BIT;   // Index operations
                        2'b11: return INSTR_128BIT;  // 4-lane operations (tacp.rvv.asp2.dsttm)
                    endcase
                end else begin
                    // For other memuop values, use conservative approach:
                    // Most tuop_110 instructions are 64-bit, only a few specific
                    // patterns are 32-bit ACE instructions
                    // TODO: Add more specific detection for 32-bit ACE patterns
                    return INSTR_64BIT;  // Conservative: assume 64-bit for unknown patterns
                end
            end
            3'b111: begin // tuop_111 - ACE operations and multi-lane instructions
                logic ace_misc_en = first_word[31];
                logic [2:0] mvop = first_word[28:26];
                
                if (ace_misc_en == 1'b0) begin
                    // Move operations (tmv.*)
                    case (mvop)
                        3'b000, 3'b001, 3'b010: return INSTR_64BIT; // tmv.rtr, tmv.trr, tmv.ttrr
                        3'b100, 3'b101: return INSTR_64BIT; // tmv.vtr, tmv.tvr  
                        3'b111: return INSTR_64BIT; // tmv.tir
                        default: return INSTR_64BIT;
                    endcase
                end else begin
                    // tuop_111 appears in lanes 2+ of multi-lane instructions
                    // For now, assume these are part of larger instructions
                    // The actual length will be determined by the first lane
                    return INSTR_32BIT; // This shouldn't be called for lane 2+
                end
            end

            default: return INSTR_32BIT;
        endcase
    endfunction

    // Function to initialize instruction collector
    function automatic instr_collector_t init_collector(input logic [31:0] first_word);
        instr_collector_t collector;

        collector.instruction_data = 128'h0;
        collector.instruction_data[31:0] = first_word;
        collector.collected_words = 3'b001; // First word collected
        collector.expected_length = get_instruction_length(first_word);
        collector.is_tile_instr = is_tile_instruction(first_word);

        // Check if instruction is complete (32-bit instructions)
        collector.is_complete = (collector.expected_length == INSTR_32BIT);

        return collector;
    endfunction

    // Function to add word to collector
    function automatic instr_collector_t add_word_to_collector(
        input instr_collector_t collector,
        input logic [31:0] word
    );
        instr_collector_t new_collector;

        new_collector = collector;

        if (!collector.is_complete && collector.collected_words < 4) begin
            case (collector.collected_words)
                3'b001: new_collector.instruction_data[63:32] = word;
                3'b010: new_collector.instruction_data[95:64] = word;
                3'b011: new_collector.instruction_data[127:96] = word;
            endcase

            new_collector.collected_words = collector.collected_words + 1;

            // Check if instruction is now complete
            case (collector.expected_length)
                INSTR_64BIT:  new_collector.is_complete = (new_collector.collected_words >= 2);
                INSTR_96BIT:  new_collector.is_complete = (new_collector.collected_words >= 3);
                INSTR_128BIT: new_collector.is_complete = (new_collector.collected_words >= 4);
                default:      new_collector.is_complete = 1'b1;
            endcase
        end

        return new_collector;
    endfunction

    // Function to get instruction bit width
    function automatic int get_instruction_bits(input instr_length_e length);
        case (length)
            INSTR_32BIT:  return 32;
            INSTR_64BIT:  return 64;
            INSTR_96BIT:  return 96;
            INSTR_128BIT: return 128;
            default:      return 32;
        endcase
    endfunction

    // Function to extract instruction fields for disassembly
    function automatic string extract_instruction_name(
        input logic [127:0] instruction_data,
        input instr_length_e length
    );
        logic [6:0] ace_op;
        logic [2:0] tuop;
        logic [5:0] memuop;
        logic [1:0] lsuop;
        logic [2:0] miscop;
        logic offseten, rmten;

        ace_op = instruction_data[6:0];

        if (ace_op != TILE_ACE_OP) begin
            return "unknown_non_tile";
        end

        tuop = instruction_data[14:12];

        case (tuop)
            3'b000: begin // Memory operations (tuop_000)
                memuop = instruction_data[30:25]; // Extract memuop field (bits 30:25)
                lsuop = instruction_data[11:10];

                case (memuop)
                    6'b000000: begin // Load operations
                        case (lsuop)
                            2'b00: begin // Linear operations (64-bit)
                                if (length == INSTR_64BIT) begin
                                    offseten = instruction_data[32+15];
                                    rmten = instruction_data[32+20];
                                    if (!offseten && !rmten)
                                        return "tld.trii.linear.u32.global";
                                    else if (!offseten && rmten)
                                        return "tld.trii.linear.u32.global.remote";
                                    else if (offseten && !rmten)
                                        return "tld.trir.linear.u32.global";
                                    else
                                        return "tld.trir.linear.u32.global.remote";
                                end
                            end
                            2'b01: return "tld.trri.stride.u32.global"; // Stride operations (64-bit)
                            2'b10: begin // Index operations (96-bit)
                                if (length == INSTR_96BIT) begin
                                    // Check offseten in the third lane (bits 64+15)
                                    offseten = instruction_data[64+15];
                                    if (!offseten)
                                        return "tld.trvi.asp.index.u32.global";
                                    else
                                        return "tld.trvr.asp.index.u32.global";
                                end
                            end
                        endcase
                    end
                    6'b001000: return "tst.trvi.index.u32.global"; // Store operations
                    6'b011100: begin // Tile copy operations (128-bit)
                        if (length == INSTR_128BIT) begin
                            // Check srctm and dsttm fields in the second lane
                            logic srctm, dsttm;
                            srctm = instruction_data[32+41]; // bit 41 in second lane
                            dsttm = instruction_data[32+42]; // bit 42 in second lane
                            if (!srctm && dsttm)
                                return "tacp.rvv.asp2.dsttm";
                            else if (srctm && !dsttm)
                                return "tacp.vvr.asp2.srctm";
                            else
                                return "tacp.rvrv.asp2.mbar.dsttm";
                        end
                    end
                endcase
            end

            3'b001: return "tmma.ttt"; // Matrix multiply (tuop_001)

            3'b010: begin // Vector operations (tuop_010)
                logic [1:0] vecuop1 = instruction_data[10:9];
                logic rsen = instruction_data[7];  // rsen is in bit 7
                logic immen = instruction_data[23]; // immen is in bit 23 based on documentation
                logic [3:0] vecuop2_52 = instruction_data[32+19:32+16]; // vecuop2[5:2] in second word
                
                if (vecuop1 == 2'b10) begin // 2-operand ALU operations (64-bit)
                    case (vecuop2_52)
                        4'b0000: begin // tadd operations
                            if (rsen == 1'b0) begin
                                if (immen == 1'b0)
                                    return "tadd.ttt"; // tile-tile-tile
                                else
                                    return "tadd.tti"; // tile-tile-immediate
                            end else begin
                                return "tadd.ttr"; // tile-tile-register
                            end
                        end
                        4'b0001: begin // tmul operations
                            if (rsen == 1'b0) begin
                                if (immen == 1'b0)
                                    return "tmul.ttt"; // tile-tile-tile
                                else
                                    return "tmul.tti"; // tile-tile-immediate
                            end else begin
                                return "tmul.ttr"; // tile-tile-register
                            end
                        end
                        4'b0011: begin // tmul operations (alternate encoding)
                            if (rsen == 1'b0) begin
                                if (immen == 1'b0)
                                    return "tmul.ttt"; // tile-tile-tile
                                else
                                    return "tmul.tti"; // tile-tile-immediate
                            end else begin
                                return "tmul.ttr"; // tile-tile-register
                            end
                        end
                        4'b1000: begin // tmin operations
                            if (rsen == 1'b0) begin
                                if (immen == 1'b0)
                                    return "tmin.ttt";
                                else
                                    return "tmin.tti";
                            end else begin
                                return "tmin.ttr";
                            end
                        end
                        4'b0010: begin // tmax operations (alternate encoding)
                            if (rsen == 1'b0) begin
                                if (immen == 1'b0)
                                    return "tmax.ttt";
                                else
                                    return "tmax.tti";
                            end else begin
                                return "tmax.ttr";
                            end
                        end
                        4'b0100: begin // tmax operations (alternate encoding 2)
                            if (rsen == 1'b0) begin
                                if (immen == 1'b0)
                                    return "tmax.ttt";
                                else
                                    return "tmax.tti";
                            end else begin
                                return "tmax.ttr";
                            end
                        end
                        4'b1001: begin // tmax operations
                            if (rsen == 1'b0) begin
                                if (immen == 1'b0)
                                    return "tmax.ttt";
                                else
                                    return "tmax.tti";
                            end else begin
                                return "tmax.ttr";
                            end
                        end
                        default: return "unknown_vec_alu";
                    endcase
                end else if (vecuop1 == 2'b11 && length == INSTR_96BIT) begin // 3-operand FMA operations (96-bit)
                    // tfma instructions - check vecuop2[5:2] for operation type
                    if (vecuop2_52 == 4'b0000) begin // FMA operations
                        // Decode operand types from encoding bits in first and third words
                        logic rs2en = instruction_data[31]; // rs2en in first word bit 31
                        logic imm2en = instruction_data[24]; // imm2en in first word bit 24  
                        logic rs3en = instruction_data[64+31]; // rs3en in third word bit 31
                        logic imm3en = instruction_data[64+24]; // imm3en in third word bit 24
                        
                        // Determine instruction variant based on operand encoding
                        if (!rs2en && !imm2en) begin // src2 is tile
                            if (!rs3en && !imm3en) // src3 is tile
                                return "tfma.tttt";
                            else if (rs3en && !imm3en) // src3 is scalar reg
                                return "tfma.tttr";
                            else if (!rs3en && imm3en) // src3 is immediate
                                return "tfma.ttti";
                        end else if (rs2en && !imm2en) begin // src2 is scalar reg
                            if (!rs3en && !imm3en) // src3 is tile
                                return "tfma.ttrt";
                            else if (rs3en && !imm3en) // src3 is scalar reg
                                return "tfma.ttrr";
                            else if (!rs3en && imm3en) // src3 is immediate
                                return "tfma.ttri";
                        end else if (!rs2en && imm2en) begin // src2 is immediate
                            if (!rs3en && !imm3en) // src3 is tile
                                return "tfma.ttit";
                            else if (rs3en && !imm3en) // src3 is scalar reg
                                return "tfma.ttir";
                            else if (!rs3en && imm3en) // src3 is immediate
                                return "tfma.ttii";
                        end
                        return "tfma.unknown_variant";
                    end else begin
                        return "unknown_fma_op";
                    end
                end else if (vecuop1 == 2'b01) begin // 1-operand operations (SFU, transpose)
                    // Check for special case: ADD operations with vecuop1=01
                    // This handles instructions like 0x027b1009e4fb
                    case (vecuop2_52)
                        4'b0000: begin // tadd operations with vecuop1=01
                            if (rsen == 1'b0) begin
                                if (immen == 1'b0)
                                    return "tadd.ttt"; // tile-tile-tile with vecuop1=01
                                else
                                    return "tadd.tti"; // tile-tile-immediate with vecuop1=01
                            end else begin
                                // tile-tile-register with vecuop1=01
                                // Check neg flags in the first word
                                logic neg1_flag = instruction_data[27];
                                logic neg2_flag = instruction_data[28];
                                
                                if (neg1_flag && neg2_flag)
                                    return "tadd.ttr.f32.neg1.neg2";
                                else if (neg1_flag)
                                    return "tadd.ttr.f32.neg1";
                                else if (neg2_flag)
                                    return "tadd.ttr.f32.neg2";
                                else
                                    return "tadd.ttr.f32";
                            end
                        end
                        default: return "talu_1src"; // Placeholder for other SFU/transpose operations
                    endcase
                end else if (vecuop1 == 2'b00) begin // Conversion operations
                    return "talu_cvt"; // Placeholder for conversion operations
                end else begin
                    return "unknown_vec";
                end
            end

            3'b110: begin // tuop_110 - multi-lane memory instructions
                logic [2:0] tuop_second;
                
                memuop = instruction_data[30:25];
                lsuop = instruction_data[11:10];
                tuop_second = instruction_data[32+14:32+12]; // tuop in second word
                
                // Check for different types of 64-bit dual-lane instructions
                if (length == INSTR_64BIT && memuop == 6'b000000 && tuop_second == 3'b001) begin
                    // Share memory linear/stride operations (tuop_110 + tuop_001)
                    case (lsuop)
                        2'b00: begin // Linear operations
                            offseten = instruction_data[32+15];
                            rmten = instruction_data[32+20];
                            if (!offseten && !rmten)
                                return "tld.trii.linear.u32.share";
                            else if (!offseten && rmten)
                                return "tld.trii.linear.u32.share.remote";
                            else if (offseten && !rmten)
                                return "tld.trir.linear.u32.share";
                            else
                                return "tld.trir.linear.u32.share.remote";
                        end
                        2'b01: begin // Stride operations
                            offseten = instruction_data[32+15];
                            if (!offseten)
                                return "tld.trri.stride.u32.share";
                            else
                                return "tld.trrr.stride.u32.share";
                        end
                        default: return "tld.share.unknown_lsuop";
                    endcase
                end else if (length == INSTR_64BIT && memuop == 6'b000001) begin
                    // Block memory operations - check second word tuop for memory space
                    if (tuop_second == 3'b000) begin
                        // Global memory space
                        case (lsuop)
                            2'b00: begin // Basic block
                                offseten = instruction_data[32+15];
                                rmten = instruction_data[32+20];
                                if (!offseten && !rmten)
                                    return "tld.tri.blk.global";
                                else if (!offseten && rmten)
                                    return "tld.tri.blk.global.remote";
                                else if (offseten && !rmten)
                                    return "tld.trr.blk.global";
                                else
                                    return "tld.trr.blk.global.remote";
                            end
                            2'b01: begin // mx48 block
                                offseten = instruction_data[32+15];
                                rmten = instruction_data[32+20];
                                if (!offseten && !rmten)
                                    return "tld.tri.blk.mx48.global";
                                else if (!offseten && rmten)
                                    return "tld.tri.blk.mx48.global.remote";
                                else if (offseten && !rmten)
                                    return "tld.trr.blk.mx48.global";
                                else
                                    return "tld.trr.blk.mx48.global.remote";
                            end
                            2'b10: begin // mx6 block
                                offseten = instruction_data[32+15];
                                rmten = instruction_data[32+20];
                                if (!offseten && !rmten)
                                    return "tld.tri.blk.mx6.global";
                                else if (!offseten && rmten)
                                    return "tld.tri.blk.mx6.global.remote";
                                else if (offseten && !rmten)
                                    return "tld.trr.blk.mx6.global";
                                else
                                    return "tld.trr.blk.mx6.global.remote";
                            end
                            default: return "tld.blk.unknown_lsuop";
                        endcase
                    end else if (tuop_second == 3'b001) begin
                        // Share memory space
                        case (lsuop)
                            2'b00: begin // Basic block
                                offseten = instruction_data[32+15];
                                rmten = instruction_data[32+20];
                                if (!offseten && !rmten)
                                    return "tld.tri.blk.share";
                                else if (!offseten && rmten)
                                    return "tld.tri.blk.share.remote";
                                else if (offseten && !rmten)
                                    return "tld.trr.blk.share";
                                else
                                    return "tld.trr.blk.share.remote";
                            end
                            2'b01: begin // mx48 block
                                offseten = instruction_data[32+15];
                                rmten = instruction_data[32+20];
                                if (!offseten && !rmten)
                                    return "tld.tri.blk.mx48.share";
                                else if (!offseten && rmten)
                                    return "tld.tri.blk.mx48.share.remote";
                                else if (offseten && !rmten)
                                    return "tld.trr.blk.mx48.share";
                                else
                                    return "tld.trr.blk.mx48.share.remote";
                            end
                            2'b10: begin // mx6 block
                                offseten = instruction_data[32+15];
                                rmten = instruction_data[32+20];
                                if (!offseten && !rmten)
                                    return "tld.tri.blk.mx6.share";
                                else if (!offseten && rmten)
                                    return "tld.tri.blk.mx6.share.remote";
                                else if (offseten && !rmten)
                                    return "tld.trr.blk.mx6.share";
                                else
                                    return "tld.trr.blk.mx6.share.remote";
                            end
                            default: return "tld.blk.share.unknown_lsuop";
                        endcase
                    end else begin
                        return "tld.blk.unknown_space";
                    end
                end else if (length == INSTR_64BIT && memuop == 6'b010001) begin
                    // tacp.commit_group operations (64-bit)
                    // Check second word tuop to confirm it's tuop_000
                    if (tuop_second == 3'b000) begin
                        return "tacp.commit_group";
                    end else begin
                        return "tacp.commit_group.invalid";
                    end
                end else if (length == INSTR_64BIT && memuop == 6'b011100) begin
                    // 64-bit tacp operations (tacp.rrr.linear)
                    case (lsuop)
                        2'b00: return "tacp.rrr.linear";  // Linear operations
                        2'b01: return "tacp.rrr.stride";  // Stride operations  
                        default: return "tacp.rrr.unknown";
                    endcase
                end else if (length == INSTR_64BIT && memuop == 6'b001000) begin
                    // TST store operations (tuop_110 + tuop_000)
                    // Check second word tuop to confirm it's tuop_000
                    if (tuop_second == 3'b000) begin
                        case (lsuop)
                            2'b00: begin // Linear operations
                                offseten = instruction_data[32+15];
                                rmten = instruction_data[32+20];
                                if (!offseten && !rmten)
                                    return "tst.trii.linear.u32.global.m4";
                                else if (!offseten && rmten)
                                    return "tst.trii.linear.u32.global.remote.m4";
                                else if (offseten && !rmten)
                                    return "tst.trir.linear.u32.global.m4";
                                else
                                    return "tst.trir.linear.u32.global.remote.m4";
                            end
                            2'b01: begin // Stride operations
                                offseten = instruction_data[32+15];
                                if (!offseten)
                                    return "tst.trri.stride.u32.global.m4";
                                else
                                    return "tst.trrr.stride.u32.global.m4";
                            end
                            2'b10: return "tst.trvi.index.u32.global.m4"; // Index operations
                            default: return "tst.unknown_lsuop";
                        endcase
                    end else begin
                        return "tst.invalid_second_tuop";
                    end
                end else if (length == INSTR_64BIT) begin
                    // Check for vector ALU operations with tuop_110 + tuop_010 pattern
                    // This handles special cases like tadd.ttr.f32.neg2
                    logic [2:0] tuop_second = instruction_data[32+14:32+12];
                    logic [1:0] vecuop1 = instruction_data[10:9];
                    logic [3:0] vecuop2_52 = instruction_data[32+19:32+16];
                    logic rsen = instruction_data[7];  // rsen is in bit 7
                    logic immen = instruction_data[23]; // immen is in bit 23 based on documentation
                    logic neg1 = instruction_data[27];
                    logic neg2 = instruction_data[28];
                    
                    if (tuop_second == 3'b010) begin
                        // SFU operations with vecuop1=01
                        if (vecuop1 == 2'b01) begin
                            // Handle special function unit operations
                            // Extract vecuop2[1:0] to determine data type
                            logic [1:0] vecuop2_10 = instruction_data[32+17:32+16]; // vecuop2[1:0] in second word
                            string data_type;
                            
                            // Determine data type based on vecuop2[1:0]
                            case (vecuop2_10)
                                2'b00: data_type = "f32";
                                2'b01: data_type = "bf16";
                                2'b10: data_type = "f16";
                                default: data_type = "f32"; // fallback
                            endcase
                            
                            case (vecuop2_52)
                                4'b0000: begin
                                    string result_sgmd;
                                    $sformat(result_sgmd, "tsgmd.tt.%s", data_type);
                                    return result_sgmd;
                                end
                                4'b0001: begin
                                    string result_sin;
                                    $sformat(result_sin, "tsin.tt.%s", data_type);
                                    return result_sin;
                                end
                                4'b0010: begin
                                    string result_cos;
                                    $sformat(result_cos, "tcos.tt.%s", data_type);
                                    return result_cos;
                                end
                                4'b0011: begin
                                    string result_exp2;
                                    $sformat(result_exp2, "texp2.tt.%s", data_type);
                                    return result_exp2;
                                end
                                4'b0100: begin
                                    string result_log2;
                                    $sformat(result_log2, "tlog2.tt.%s", data_type);
                                    return result_log2;
                                end
                                4'b0110: begin
                                    string result_rcp;
                                    $sformat(result_rcp, "trcp.tt.%s", data_type);
                                    return result_rcp;
                                end
                                4'b0111: begin
                                    string result_sqrt;
                                    $sformat(result_sqrt, "tsqrt.tt.%s", data_type);
                                    return result_sqrt;
                                end
                                4'b1000: begin
                                    string result_rsqrt;
                                    $sformat(result_rsqrt, "trsqrt.tt.%s", data_type);
                                    return result_rsqrt;
                                end
                                4'b1001: begin
                                    string result_tanh;
                                    $sformat(result_tanh, "ttanh.tt.%s", data_type);
                                    return result_tanh;
                                end
                                default: return "talu_1src";    // Generic SFU placeholder
                            endcase
                        end
                        
                        // Vector ALU operations with vecuop1=10
                        if (vecuop1 == 2'b10) begin
                            case (vecuop2_52)
                            4'b0000: begin // tadd operations
                                if (rsen && !immen) begin
                                    // tadd.ttr variants
                                    if (neg1 && neg2)
                                        return "tadd.ttr.f32.neg1.neg2";
                                    else if (neg1)
                                        return "tadd.ttr.f32.neg1";
                                    else if (neg2)
                                        return "tadd.ttr.f32.neg2";
                                    else
                                        return "tadd.ttr.f32";
                                end else if (!rsen && !immen) begin
                                    // tadd.ttt variants - also handle special vecuop1=01 case
                                    if (vecuop1 == 2'b01) begin
                                        // Special case for vecuop1=01 with rsen=0, immen=0
                                        // This is still a tadd.ttr variant based on the register encoding
                                        if (neg1 && neg2)
                                            return "tadd.ttr.f32.neg1.neg2";
                                        else if (neg1)
                                            return "tadd.ttr.f32.neg1";
                                        else if (neg2)
                                            return "tadd.ttr.f32.neg2";
                                        else
                                            return "tadd.ttr.f32";
                                    end else begin
                                        // Standard tadd.ttt variants
                                        if (neg1 && neg2)
                                            return "tadd.ttt.f32.neg1.neg2";
                                        else if (neg1)
                                            return "tadd.ttt.f32.neg1";
                                        else if (neg2)
                                            return "tadd.ttt.f32.neg2";
                                        else
                                            return "tadd.ttt.f32";
                                    end
                                end else if (!rsen && immen) begin
                                    // tadd.tti variants
                                    if (neg1 && neg2)
                                        return "tadd.tti.f32.neg1.neg2";
                                    else if (neg1)
                                        return "tadd.tti.f32.neg1";
                                    else if (neg2)
                                        return "tadd.tti.f32.neg2";
                                    else
                                        return "tadd.tti.f32";
                                end
                            end
                            4'b0001: begin // tmul operations
                                if (rsen && !immen) begin
                                    // tmul.ttr variants
                                    if (neg1 && neg2)
                                        return "tmul.ttr.f32.neg1.neg2";
                                    else if (neg1)
                                        return "tmul.ttr.f32.neg1";
                                    else if (neg2)
                                        return "tmul.ttr.f32.neg2";
                                    else
                                        return "tmul.ttr.f32";
                                end else if (!rsen && !immen) begin
                                    // tmul.ttt variants
                                    return "tmul.ttt.f32";
                                end else if (!rsen && immen) begin
                                    // tmul.tti variants
                                    return "tmul.tti.f32";
                                end
                            end
                            4'b0011: begin // tmul operations (alternate encoding)
                                if (rsen && !immen) begin
                                    // tmul.ttr variants
                                    if (neg1 && neg2)
                                        return "tmul.ttr.f32.neg1.neg2";
                                    else if (neg1)
                                        return "tmul.ttr.f32.neg1";
                                    else if (neg2)
                                        return "tmul.ttr.f32.neg2";
                                    else
                                        return "tmul.ttr.f32";
                                end else if (!rsen && !immen) begin
                                    // tmul.ttt variants
                                    return "tmul.ttt.f32";
                                end else if (!rsen && immen) begin
                                    // tmul.tti variants
                                    return "tmul.tti.f32";
                                end
                            end
                            4'b1000: begin // tmin operations
                                if (rsen && !immen)
                                    return "tmin.ttr.f32";
                                else if (!rsen && !immen)
                                    return "tmin.ttt.f32";
                                else if (!rsen && immen)
                                    return "tmin.tti.f32";
                            end
                            4'b0010: begin // tmax operations (alternate encoding)
                                if (rsen && !immen)
                                    return "tmax.ttr.f32";
                                else if (!rsen && !immen)
                                    return "tmax.ttt.f32";
                                else if (!rsen && immen)
                                    return "tmax.tti.f32";
                            end
                            4'b0100: begin // tmax operations (alternate encoding 2)
                                if (rsen && !immen)
                                    return "tmax.ttr.f32";
                                else if (!rsen && !immen)
                                    return "tmax.ttt.f32";
                                else if (!rsen && immen)
                                    return "tmax.tti.f32";
                            end
                            4'b1001: begin // tmax operations
                                if (rsen && !immen)
                                    return "tmax.ttr.f32";
                                else if (!rsen && !immen)
                                    return "tmax.ttt.f32";
                                else if (!rsen && immen)
                                    return "tmax.tti.f32";
                            end
                            default: return "unknown_vec_alu_110";
                        endcase
                        end
                    end else begin
                        // Detect tmv.vtr/tmv.tvr patterns that use tuop_110 in lane 0 and tuop_111 in lane 1
                        if (length == INSTR_64BIT) begin
                            logic [2:0] tuop_second2 = instruction_data[32+14:32+12];
                            if (tuop_second2 == 3'b111) begin
                                logic ace_misc_en2 = instruction_data[32+31];
                                logic [2:0] mvop2 = instruction_data[32+28:32+26];
                                logic [5:0] func6_2 = instruction_data[32+25:32+20];
                                if (ace_misc_en2 == 1'b0) begin
                                    // Move ops indicated in second lane
                                    if (mvop2 == 3'b000) begin
                                        // tmv.vtr.* (alternate encoding with mvop=000)
                                        case (func6_2)
                                            6'b100000: return "tmv.vtr.e8";
                                            6'b100101: return "tmv.vtr.e16";
                                            6'b100110: return "tmv.vtr.e32";
                                            default:   return "tmv.vtr";
                                        endcase
                                    end else if (mvop2 == 3'b100) begin
                                        // tmv.vtr.*
                                        case (func6_2)
                                            6'b100000: return "tmv.vtr.e8";
                                            6'b100101: return "tmv.vtr.e16";
                                            6'b100110: return "tmv.vtr.e32";
                                            default:   return "tmv.vtr";
                                        endcase
                                    end else if (mvop2 == 3'b101) begin
                                        // tmv.tvr.*
                                        case (func6_2)
                                            6'b100000: return "tmv.tvr.e8";
                                            6'b100101: return "tmv.tvr.e16";
                                            6'b100110: return "tmv.tvr.e32";
                                            default:   return "tmv.tvr";
                                        endcase
                                    end
                                end
                            end
                        end
                        // Other tuop_110 patterns we haven't implemented yet
                        return "tld_64bit_unimplemented";
                    end
                end else begin
                    // Fallback to generic handling for other cases
                    case (length)
                        INSTR_96BIT: return "tld_96bit";   // Generic 96-bit memory operation
                        INSTR_128BIT: return "tacp_128bit"; // Generic 128-bit tile copy
                        default: return "unknown_tuop110";
                    endcase
                end
            end



            3'b100: begin // CSR operations
                logic [1:0] rw = instruction_data[31:30];
                case (rw)
                    2'b00: return "tcsrw.i";
                    2'b01: return "tcsrr.r";
                    2'b10: return "tcsrw.r";
                    default: return "unknown_csr";
                endcase
            end

            3'b101: begin // Sync operations (tuop_101)
                logic [2:0] ctrluop;
                logic [2:0] waitop;

                ctrluop = instruction_data[25:23];
                waitop = instruction_data[30:28];

                case (ctrluop)
                    3'b000: begin // tsync operations
                        return "tsync.i";
                    end
                    3'b001: begin // twait operations
                        case (waitop)
                            3'b000: return "twait.i.ls";          // load/store immediate
                            3'b001: return "twait.r.ls";          // load/store register
                            3'b010: return "twait.r.tacp_cg";     // tacp commit group
                            3'b011: return "twait.r.rmtfence";    // remote fence
                            3'b100: return "tkill.r";             // kill register
                            3'b111: begin // basic twait
                                if (instruction_data[26] == 1'b1)
                                    return "twait.mem";
                                else
                                    return "twait";
                            end
                            default: return "twait.unknown";
                        endcase
                    end
                    3'b010: return "trmtfence.r";             // remote fence register
                    default: return "unknown_sync";
                endcase
            end

            3'b111: begin // ACE operations (tuop_111)
                logic [2:0] miscop = instruction_data[28:26];
                logic [2:0] mvop = instruction_data[28:26]; // same position as miscop
                logic ace_misc_en = instruction_data[31];

                if (ace_misc_en == 1'b1) begin
                    case (miscop)
                        3'b000: return "ace_bsync";  // ace_bsync
                        3'b010: return "ace_nbsync"; // ace_nbsync
                        3'b100: return "taspin.float"; // taspin.float
                        3'b111: return "taspin.vec"; // taspin.vec
                        default: return "unknown_ace_misc";
                    endcase
                end else begin
                    // Move operations (tmv.*)
                    case (mvop)
                        3'b000: return "tmv.rtr"; // register to tile register
                        3'b001: return "tmv.trr"; // tile register to register
                        3'b010: return "tmv.ttrr"; // tile to tile with register
                        3'b100: begin // vector to tile register
                            logic [5:0] func6 = instruction_data[32+25:32+20]; // func6 in second word
                            case (func6)
                                6'b100000: return "tmv.vtr.e8";
                                6'b100101: return "tmv.vtr.e16";
                                6'b100110: return "tmv.vtr.e32";
                                default: return "tmv.vtr";
                            endcase
                        end
                        3'b101: begin // tile register to vector
                            logic [5:0] func6 = instruction_data[32+25:32+20]; // func6 in second word
                            case (func6)
                                6'b100000: return "tmv.tvr.e8";
                                6'b100101: return "tmv.tvr.e16";
                                6'b100110: return "tmv.tvr.e32";
                                default: return "tmv.tvr";
                            endcase
                        end
                        3'b111: return "tmv.tir"; // tile immediate to register
                        default: return "unknown_tmv";
                    endcase
                end
            end

            default: return "unknown_tile";
        endcase

        return "unknown";
    endfunction

    // Function to format operands
    function automatic string format_operands(
        input logic [127:0] instruction_data,
        input instr_length_e length,
        input string instr_name
    );
        logic [7:0] td;
        logic [4:0] rs1, rs2;
        string operands = "";

        // Extract fields based on instruction type and length
        case (length)
            INSTR_32BIT: begin
                // 32-bit instructions (CSR, sync, ACE)
                if (instr_name.substr(0, 4) == "tcsr") begin
                    // CSR instructions have different layout
                    // Fields are in the single 32-bit word
                end
            end

            INSTR_64BIT: begin
                // Check for block memory instructions by examining the actual instruction fields
                // instead of relying on string matching which might be unreliable
                logic [2:0] tuop_first = instruction_data[14:12];
                logic [5:0] memuop_field = instruction_data[30:25];
                logic [2:0] tuop_second = instruction_data[32+14:32+12];

                if (tuop_first == 3'b110 && memuop_field == 6'b000000 && tuop_second == 3'b001) begin
                    // Share memory linear/stride operations (tuop_110 + tuop_001)
                    td = instruction_data[32+31:32+24];  // Td field in second word bits [31:24]
                    rs1 = instruction_data[32+19:32+15]; // rs1 field in second word bits [19:15]
                end else if (tuop_first == 3'b110 && memuop_field == 6'b000001 && tuop_second == 3'b001) begin
                    // This is definitely a tld.trr.blk.* instruction based on field values
                    // Use the corrected field positions for block memory instructions
                    td = instruction_data[32+30:32+23];  // Td field in second word bits [30:23]
                    rs1 = instruction_data[32+19:32+15]; // rs1 field in second word bits [19:15]
                    rs2 = instruction_data[24:20];       // rs3 field in first word bits [24:20]
                end else if (instr_name.substr(0, 3) == "tld" && instr_name.substr(4, 3) == "trr") begin
                    // Fallback: string-based detection for other trr instructions
                    td = instruction_data[32+30:32+23];  // Td field in second word bits [30:23]
                    rs1 = instruction_data[32+19:32+15]; // rs1 field in second word bits [19:15]
                    rs2 = instruction_data[24:20];       // rs3 field in first word bits [24:20]
                end else begin
                    // Standard 64-bit layout for other instructions
                    td = instruction_data[32+7:32+0];    // Td field in second word
                    rs1 = instruction_data[32+20:32+16]; // rs1 field in second word
                end
            end

            INSTR_96BIT: begin
                // 3-lane instructions (index operations)
                td = instruction_data[32+23:32+16];  // Td field in second word bits [23:16]
                rs1 = instruction_data[32+15:32+11]; // rs1 field in second word bits [15:11]
            end

            INSTR_128BIT: begin
                // 4-lane instructions (tile copy operations)
                // Fields are distributed across multiple lanes
                rs1 = instruction_data[32+19:32+15]; // rs1 field in second word
            end
        endcase

        // Format based on instruction type and length
        case (length)
            INSTR_32BIT: begin
                if (instr_name.substr(0, 4) == "tcsr") begin
                    // CSR instructions have different formats based on rw field
                    logic [1:0] rw = instruction_data[31:30];
                    logic [8:0] csr_addr = instruction_data[28:20]; // 9-bit CSR address
                    logic [4:0] rd = instruction_data[11:7];
                    logic [4:0] rs1_or_imm = instruction_data[19:15];

                    case (rw)
                        2'b00: begin // tcsrw.i - write immediate
                            $sformat(operands, "0x%0x", rs1_or_imm); // Only immediate value
                        end
                        2'b01: begin // tcsrr.r - read to register
                            $sformat(operands, "x%0d", rd); // Only destination register
                        end
                        2'b10: begin // tcsrw.r - write from register
                            $sformat(operands, "x%0d", rs1_or_imm); // Only source register
                        end
                        default: operands = "unknown";
                    endcase
                end else if (instr_name == "twait" || instr_name == "twait.mem") begin
                    // Basic twait instructions - no operands
                    operands = "";
                end else if (instr_name == "twait.i.load.global" || instr_name == "twait.i.load.share" ||
                           instr_name == "twait.i.store.global" || instr_name == "twait.i.store.share") begin
                    // twait.i.* instructions - immediate operand
                    logic [7:0] cnt = instruction_data[22:15]; // cnt field bits [22:15]
                    $sformat(operands, "%0d", cnt);
                end else if (instr_name == "twait.r.load.global" || instr_name == "twait.r.load.share" ||
                           instr_name == "twait.r.store.global" || instr_name == "twait.r.store.share" ||
                           instr_name == "twait.r.tacp_cg" || instr_name == "twait.r.rmtfence") begin
                    // twait.r.* instructions - register operand
                    logic [4:0] rs1 = instruction_data[19:15]; // rs1 field bits [19:15]
                    $sformat(operands, "x%0d", rs1);
                end else if (instr_name == "tsync.i") begin
                    // tsync instructions
                    logic [4:0] sync_id = instruction_data[19:15]; // sync_id field bits [19:15]
                    $sformat(operands, "%0d", sync_id);
                end else if (instr_name == "tkill.r") begin
                    // tkill instructions
                    logic [4:0] rs1 = instruction_data[19:15]; // rs1 field bits [19:15]
                    $sformat(operands, "x%0d", rs1);
                end else if (instr_name == "ace_bsync" || instr_name == "ace_nbsync") begin
                    // ACE sync instructions: ace_bsync/ace_nbsync sync_id
                    logic [4:0] sync_id = instruction_data[19:15]; // sync_id field bits [19:15]
                    $sformat(operands, "x%0d", sync_id);
                end else begin
                    // Other 32-bit instructions
                    operands = "0";
                end
            end

            INSTR_64BIT: begin
                // Format based on instruction name for 64-bit instructions
                if (instr_name.substr(0, 3) == "tmv") begin
                    // tmv operations have different operand formats
                    if (instr_name == "tmv.rtr") begin
                        // tmv.rtr rd, ts1, rs2
                        logic [4:0] rd = instruction_data[32+11:32+7]; // rd in second word
                        logic [7:0] ts1 = instruction_data[23:16]; // ts1 in first word
                        logic [4:0] rs2 = instruction_data[24:20]; // rs2 in first word
                        $sformat(operands, "x%0d, t%0d, x%0d", rd, ts1, rs2);
                    end else if (instr_name == "tmv.trr") begin
                        // tmv.trr td, rs1, rs2
                        logic [7:0] td_tmv = instruction_data[23:16]; // td in first word
                        logic [4:0] rs1_tmv = instruction_data[32+19:32+15]; // rs1 in second word
                        logic [4:0] rs2_tmv = instruction_data[24:20]; // rs2 in first word
                        $sformat(operands, "t%0d, x%0d, x%0d", td_tmv, rs1_tmv, rs2_tmv);
                    end else if (instr_name.substr(0, 8) == "tmv.vtr") begin
                        // tmv.vtr.*: expect operands as vD, Tn, zero/xr
                        logic [4:0] vD = instruction_data[32+11:32+7];   // vector reg in second word
                        logic [7:0] Tn = instruction_data[23:16];        // tile index in first word (Ts1 field)
                        logic [4:0] rs2_vtr = instruction_data[24:20];   // rs2 in first word
                        if (rs2_vtr == 5'd0)
                            $sformat(operands, "v%0d, T%0d, zero", vD, Tn);
                        else
                            $sformat(operands, "v%0d, T%0d, x%0d", vD, Tn, rs2_vtr);
                    end else if (instr_name.substr(0, 6) == "tmv.vt" || instr_name.substr(0, 6) == "tmv.tv") begin
                        // Other vector-tile operations
                        // Fall back to generic ordering: tD, vS
                        logic [7:0] tD_generic = instruction_data[23:16];         // tile field in first word
                        logic [4:0] vS_generic = instruction_data[32+11:32+7];    // vector field in second word
                        $sformat(operands, "t%0d, v%0d", tD_generic, vS_generic);
                    end else begin
                        $sformat(operands, "t%0d, x%0d", td, rs1);
                    end
                end else if (instr_name.substr(0, 4) == "tadd" || instr_name.substr(0, 4) == "tmul" ||
                           instr_name.substr(0, 4) == "tmin" || instr_name.substr(0, 4) == "tmax") begin
                    // Vector ALU operations
                    logic [7:0] td_vec = instruction_data[32+7:32+0]; // Td in second word
                    logic [7:0] ts1_vec = instruction_data[32+23:32+16]; // Ts1 in second word
                    
                    if (instr_name.substr(4, 3) == "ttt") begin
                        // Three tile operands
                        logic [7:0] ts2_vec = instruction_data[23:16]; // Ts2 in first word
                        $sformat(operands, "t%0d, t%0d, t%0d", td_vec, ts1_vec, ts2_vec);
                    end else if (instr_name.substr(4, 3) == "tti") begin
                        // Two tiles + immediate
                        logic [4:0] imm = instruction_data[24:20]; // imm in first word
                        $sformat(operands, "t%0d, t%0d, #%0d", td_vec, ts1_vec, imm);
                    end else if (instr_name.substr(4, 3) == "ttr") begin
                        // Two tiles + register
                        logic [4:0] rs2_vec = instruction_data[24:20]; // rs2 in first word
                        $sformat(operands, "t%0d, t%0d, x%0d", td_vec, ts1_vec, rs2_vec);
                    end else begin
                        $sformat(operands, "t%0d, t%0d", td_vec, ts1_vec);
                    end
                end else if (instr_name.substr(0, 5) == "tsgmd" || instr_name.substr(0, 4) == "tsin" ||
                           instr_name.substr(0, 4) == "tcos" || instr_name.substr(0, 5) == "texp2" ||
                           instr_name.substr(0, 5) == "tlog2" || instr_name.substr(0, 4) == "trcp" ||
                           instr_name.substr(0, 5) == "tsqrt" || instr_name.substr(0, 6) == "trsqrt" ||
                           instr_name.substr(0, 5) == "ttanh") begin
                    // SFU operations - format: instruction Td, Ts1
                    logic [7:0] td_sfu = instruction_data[32+7:32+0]; // Td in second word
                    logic [7:0] ts1_sfu = instruction_data[32+23:32+16]; // Ts1 in second word
                    $sformat(operands, "T%0d, T%0d", td_sfu, ts1_sfu);
                end else if (instr_name == "tacp.commit_group") begin
                    // tacp.commit_group has no operands
                    operands = "";
                end
                else if (instr_name.substr(0, 3) == "tld" && instr_name.len() >= 25 && instr_name.substr(0, 25) == "tld.trii.linear.u32.share") begin
                    // tld.trii.linear.u32.share instructions: Td, (rs1), imm1, imm2
                    // Extract imm1 and imm2 from first word - they are both 0 for this type
                    logic [4:0] imm1 = instruction_data[22:18]; // imm1 field in first word
                    logic [4:0] imm2 = instruction_data[17:13]; // imm2 field in first word
                    $sformat(operands, "T%0d, (t%0d), %0d, %0d", td, rs1, imm1, imm2);
                end else begin
                    // Other 64-bit instructions: use original generic format
                    $sformat(operands, "t%0d, (x%0d)", td, rs1);
                end
            end

            INSTR_96BIT: begin
                // Simplified 96-bit instructions: generic format
                $sformat(operands, "t%0d, (x%0d)", td, rs1);
            end

            INSTR_128BIT: begin
                // Simplified 128-bit instructions: generic format
                $sformat(operands, "t%0d, t%0d, x%0d", td, td, rs1);
            end
        endcase

        return operands;
    endfunction

    // Main disassembly function
    function automatic string disassemble_instruction(
        input logic [127:0] instruction_data,
        input instr_length_e length
    );
        string instr_name, operands, result;

        instr_name = extract_instruction_name(instruction_data, length);
        operands = format_operands(instruction_data, length, instr_name);

        if (operands != "")
            $sformat(result, "%s %s", instr_name, operands);
        else
            result = instr_name;

        return result;
    endfunction

endpackage