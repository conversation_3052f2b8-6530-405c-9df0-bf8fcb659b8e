#!/usr/bin/env python3
"""
Tile Extension ISA Disassembler

This program disassembles binary instructions into readable assembly strings
for the Tile Extension ISA based on the encoding definitions in the project.
"""

import re
import glob
import os
import yaml
import sys
import json
from typing import Dict, List, Tuple, Optional, Any
from dataclasses import dataclass


@dataclass
class InstructionField:
    """Represents a field in an instruction encoding"""
    name: str
    bits: int
    attr: str
    start_bit: int
    end_bit: int


@dataclass
class InstructionDefinition:
    """Represents a complete instruction definition"""
    name: str
    fields: List[InstructionField]
    encoding: str
    match_value: int
    match_mask: int
    file_path: str
    total_bits: int


class TileDisassembler:
    """Main disassembler class for Tile Extension ISA"""

    def __init__(self):
        self.instructions: Dict[str, InstructionDefinition] = {}
        self.load_instruction_definitions()

    def load_instruction_definitions(self):
        """Load all instruction definitions from encode directories"""
        print("Loading instruction definitions...")

        # Get all encoding files
        all_files = glob.glob("./encode/**/*.md", recursive=True)
        ace_all_files = glob.glob("./ace_encode/**/*.md", recursive=True)

        for md_file in all_files + ace_all_files:
            if not "encode.md" in md_file:
                self._process_instruction_file(md_file)

        print(f"Loaded {len(self.instructions)} instruction definitions")

    def _process_instruction_file(self, file_path: str):
        """Process a single instruction definition file"""
        try:
            with open(file_path, "r") as f:
                content = f.read()

            # Extract instruction name from comment
            instr_name = "unknown"
            yaml_content = ""

            for line in content.splitlines():
                if line.startswith("```"):
                    continue
                elif line.startswith("//"):
                    instr_name = line.strip().replace("//", "").strip()
                else:
                    yaml_content += line + "\n"

            # Parse YAML content
            try:
                data = yaml.safe_load(yaml_content)
                if not data or "reg" not in data:
                    return

                # Process fields - wavedrom fields are listed from LSB to MSB
                # The first field in the list is at the lowest bit position [0:bits-1]
                fields = []
                total_bits = 0

                # First pass: calculate total bits
                for field_data in data["reg"]:
                    total_bits += field_data.get("bits", 0)

                # Second pass: assign bit positions
                # Fields are listed from LSB to MSB, so process them in order
                pos = 0  # Start from LSB (bit 0)
                for field_data in data["reg"]:
                    bits = field_data.get("bits", 0)
                    name = field_data.get("name", "")
                    attr = field_data.get("attr", "")

                    field = InstructionField(
                        name=name,
                        bits=bits,
                        attr=attr,
                        start_bit=pos,
                        end_bit=pos + bits - 1
                    )
                    fields.append(field)
                    pos += bits

                # Calculate encoding pattern and match values
                encoding, match_value, match_mask = self._calculate_encoding(fields)

                # Create instruction definition
                instr_def = InstructionDefinition(
                    name=instr_name,
                    fields=fields,
                    encoding=encoding,
                    match_value=match_value,
                    match_mask=match_mask,
                    file_path=file_path,
                    total_bits=total_bits
                )

                self.instructions[instr_name] = instr_def

            except yaml.YAMLError as e:
                print(f"Warning: Failed to parse YAML in {file_path}: {e}")

        except Exception as e:
            print(f"Warning: Failed to process {file_path}: {e}")

    def _calculate_encoding(self, fields: List[InstructionField]) -> Tuple[str, int, int]:
        """Calculate encoding pattern, match value and mask from fields"""
        # Use the same algorithm as the original check_encoding.py
        # Fields are processed in order, with each field prepended to the encoding string
        encoding = ""

        for field in fields:
            if field.attr:
                # Fixed bits
                if len(field.attr) != field.bits:
                    print(f"Warning: attr length {len(field.attr)} != bits {field.bits} for field {field.name}")
                    field_encoding = "x" * field.bits
                else:
                    field_encoding = field.attr
            elif field.name:
                # Variable bits
                field_encoding = "-" * field.bits
            else:
                # Unused bits (holes)
                field_encoding = "^" * field.bits

            # Prepend to encoding string (same as original: encoding = attr + encoding)
            encoding = field_encoding + encoding

        # Calculate match value and mask (same as original)
        match_bits = encoding.replace('-', '0').replace('^', '0').replace('x', '0')

        # For mask: fixed bits (0,1) become 1, variable bits (-,^,x) become 0
        mask_bits = ''.join('1' if c in '01' else '0' for c in encoding)

        # Convert to integers
        try:
            match_value = int(match_bits, 2) if match_bits and all(c in '01' for c in match_bits) else 0
            match_mask = int(mask_bits, 2) if mask_bits and all(c in '01' for c in mask_bits) else 0
        except ValueError:
            match_value = 0
            match_mask = 0

        return encoding, match_value, match_mask

    def disassemble_instruction(self, binary_data: int, bit_width: int = 32) -> Optional[str]:
        """
        Disassemble a binary instruction into assembly string

        Args:
            binary_data: The binary instruction as integer
            bit_width: Width of instruction in bits (32 or 64)

        Returns:
            Assembly string or None if no match found
        """
        # Find matching instruction
        matching_instructions = []

        for instr_name, instr_def in self.instructions.items():
            # Check if bit width matches
            if instr_def.total_bits != bit_width:
                continue

            # Check if instruction matches
            if (binary_data & instr_def.match_mask) == instr_def.match_value:
                matching_instructions.append(instr_def)

        if not matching_instructions:
            return None

        # If multiple matches, prefer the most specific one (highest mask value)
        best_match = max(matching_instructions, key=lambda x: x.match_mask)

        # Extract fields and format instruction
        return self._format_instruction(binary_data, best_match)

    def _format_instruction(self, binary_data: int, instr_def: InstructionDefinition) -> str:
        """Format instruction with extracted field values"""
        # Extract all fields (both variable and fixed for verification)
        field_values = {}
        for field in instr_def.fields:
            if field.name:  # Named field
                # Extract field value
                mask = (1 << field.bits) - 1
                value = (binary_data >> field.start_bit) & mask
                field_values[field.name] = value

        # Format instruction based on type and available fields
        return self._format_assembly_syntax(instr_def.name, field_values, instr_def.fields)

    def _format_assembly_syntax(self, instr_name: str, field_values: Dict[str, int], fields: List[InstructionField]) -> str:
        """Format instruction using proper assembly syntax"""
        # Get only variable fields (not fixed)
        variable_fields = {}
        for name, value in field_values.items():
            field_obj = next((f for f in fields if f.name == name), None)
            if field_obj and not field_obj.attr:  # Variable field
                variable_fields[name] = value

        # Format based on instruction type
        if 'tld' in instr_name or 'tst' in instr_name:  # Memory instructions
            return self._format_memory_instruction(instr_name, variable_fields)
        elif 'tmma' in instr_name:  # Matrix multiply instructions
            return self._format_mma_instruction(instr_name, variable_fields)
        elif 'tcsrr' in instr_name or 'tcsrw' in instr_name:  # CSR instructions
            return self._format_csr_instruction(instr_name, variable_fields)
        elif 'twait' in instr_name or 'tsync' in instr_name:  # Sync instructions
            return self._format_sync_instruction(instr_name, variable_fields)
        elif 'tsfuop' in instr_name:  # Special Function Unit instructions
            return self._format_sfu_instruction(instr_name, variable_fields)
        elif instr_name.startswith('tmv.vtr') or instr_name.startswith('tmv.tvr'):
            return self._format_vtr_tvr_instruction(instr_name, variable_fields)
        elif 'tmax.ttt.alt' in instr_name:
            # Handle alternate tmax.ttt encoding - rename to standard tmax.ttt.f32
            return self._format_tmax_alt_instruction(variable_fields)
        elif 'tmv.vtr.e32.alt' in instr_name:
            # Handle alternate tmv.vtr.e32 encoding - rename to standard tmv.vtr.e32
            return self._format_tmv_vtr_alt_instruction(variable_fields)
        elif 'tmul.ttr.alt' in instr_name:
            # Handle alternate tmul.ttr encoding - rename to standard tmul.ttr.f32
            return self._format_tmul_ttr_alt_instruction(variable_fields)
        elif 'tmax.ttt.alt2' in instr_name:
            # Handle alternate tmax.ttt encoding 2 - rename to standard tmax.ttt.f32
            return self._format_tmax_ttt_alt2_instruction(variable_fields)
        else:
            return self._format_generic_instruction(instr_name, variable_fields)

    def _format_memory_instruction(self, instr_name: str, fields: Dict[str, int]) -> str:
        """Format memory load/store instructions"""
        result = instr_name

        # Standard memory instruction format: instr Td, (rs1), rs2/imm
        operands = []

        if 'Td' in fields:
            operands.append(f"t{fields['Td']}")

        if 'rs1' in fields:
            operands.append(f"(x{fields['rs1']})")

        if 'rs2' in fields:
            operands.append(f"x{fields['rs2']}")
        elif 'rs3' in fields:
            operands.append(f"x{fields['rs3']}")

        # Add immediate values if present
        for name, value in fields.items():
            if 'imm' in name.lower() and value != 0:
                operands.append(f"0x{value:x}")

        if operands:
            result += " " + ", ".join(operands)

        return result

    def _format_mma_instruction(self, instr_name: str, fields: Dict[str, int]) -> str:
        """Format matrix multiply instructions"""
        result = instr_name

        # MMA format: tmma Td, Ts1, Ts2
        operands = []

        if 'Td' in fields:
            operands.append(f"t{fields['Td']}")
        if 'Ts1' in fields:
            operands.append(f"t{fields['Ts1']}")
        if 'Ts2' in fields:
            operands.append(f"t{fields['Ts2']}")

        if operands:
            result += " " + ", ".join(operands)

        return result

    def _format_csr_instruction(self, instr_name: str, fields: Dict[str, int]) -> str:
        """Format CSR instructions"""
        result = instr_name

        operands = []

        if 'rd' in fields:
            operands.append(f"x{fields['rd']}")
        if 'csr_addr' in fields:
            operands.append(f"0x{fields['csr_addr']:x}")
        if 'rs1' in fields:
            operands.append(f"x{fields['rs1']}")
        elif 'imm_1' in fields:
            operands.append(f"0x{fields['imm_1']:x}")

        if operands:
            result += " " + ", ".join(operands)

        return result

    def _format_sync_instruction(self, instr_name: str, fields: Dict[str, int]) -> str:
        """Format synchronization instructions"""
        result = instr_name

        operands = []

        if 'sync_id' in fields:
            operands.append(f"0x{fields['sync_id']:x}")
        if 'cnt' in fields:
            operands.append(f"0x{fields['cnt']:x}")
        if 'rs1' in fields:
            operands.append(f"x{fields['rs1']}")

        if operands:
            result += " " + ", ".join(operands)

        return result

    def _format_sfu_instruction(self, instr_name: str, fields: Dict[str, int]) -> str:
        """Format Special Function Unit instructions"""
        # SFU operation mapping based on vecuop2[5:2]
        sfu_operations = {
            0: 'tsgmd',    # b'0000: SGMD
            1: 'tsin',     # b'0001: SIN
            2: 'tcos',     # b'0010: COS
            3: 'texp2',    # b'0011: EXP2
            4: 'tlog2',    # b'0100: LOG2
            6: 'trcp',     # b'0110: RCP
            7: 'tsqrt',    # b'0111: SQRT
            8: 'trsqrt',   # b'1000: RSQRT
            9: 'ttanh'     # b'1001: TANH
        }

        # Determine the data type from the instruction name since vecuop2[1:0] is fixed
        data_type = 'f32'  # default
        if 'fp32' in instr_name or 'f32' in instr_name:
            data_type = 'f32'
        elif 'bf16' in instr_name:
            data_type = 'bf16'
        elif 'f16' in instr_name:
            data_type = 'f16'

        # Extract vecuop2[5:2] from the fields to determine the operation
        if 'vecuop2[5:2]' in fields:
            sfu_op_code = fields['vecuop2[5:2]']

            # Get the specific operation
            operation = sfu_operations.get(sfu_op_code, 'tsfuop')

            # Construct the proper instruction name
            result = f"{operation}.tt.{data_type}"
        else:
            result = instr_name

        # Format operands - SFU instructions typically use Td, Ts1
        operands = []
        if 'Td' in fields:
            operands.append(f"T{fields['Td']}")
        if 'Ts1' in fields:
            operands.append(f"T{fields['Ts1']}")

        if operands:
            result += " " + ", ".join(operands)

        return result

    def _format_generic_instruction(self, instr_name: str, fields: Dict[str, int]) -> str:
        """Format generic instructions"""
        result = instr_name

        if fields:
            field_strs = []
            for name, value in fields.items():
                if name in ['Td', 'Ts1', 'Ts2']:  # Tile registers
                    field_strs.append(f"t{value}")
                elif name in ['rs1', 'rs2', 'rs3', 'rs4', 'rd']:  # Scalar registers
                    field_strs.append(f"x{value}")
                elif 'imm' in name.lower() or 'addr' in name.lower():  # Immediate values
                    if value == 0:
                        field_strs.append("0")
                    else:
                        field_strs.append(f"0x{value:x}")
                else:
                    field_strs.append(f"{value}")

            if field_strs:
                result += " " + ", ".join(field_strs)
    def _format_vtr_tvr_instruction(self, instr_name: str, fields: Dict[str, int]) -> str:
        """Format tmv.vtr/tmv.tvr instructions.
        Expect operands order: vD, Tn, zero/xr for vtr; vD, Tn, zero/xr similarly for uniformity.
        Fields may be named vd/vs1, Ts1/Td, rs2 depending on definition file."""
        result = instr_name
        vD = None
        Tn = None
        rs2 = None
        # Map common field names from definitions
        if 'vd' in fields:
            vD = fields['vd']
        elif 'vs1' in fields:
            vD = fields['vs1']
        elif 'vdx' in fields:
            vD = fields['vdx']
        if 'Ts1' in fields:
            Tn = fields['Ts1']
        elif 'Td' in fields:
            Tn = fields['Td']
        if 'rs2' in fields:
            rs2 = fields['rs2']
        # Build operands text
        ops = []
        if vD is not None:
            ops.append(f"v{vD}")
        if Tn is not None:
            ops.append(f"T{Tn}")
        # third operand is zero/xr if present
        if rs2 is not None:
            if rs2 == 0:
                ops.append("zero")
            else:
                ops.append(f"x{rs2}")
        if ops:
            result += " " + ", ".join(ops)
    def _format_tmax_alt_instruction(self, fields: Dict[str, int]) -> str:
        """Format alternate tmax.ttt instruction as standard tmax.ttt.f32"""
        result = "tmax.ttt.f32"

        # Extract tile register fields
        Td = fields.get('Td', 0)
        Ts1 = fields.get('Ts1', 0)
        Ts2 = fields.get('Ts2', 0)

        # Format as tmax.ttt.f32 Td, Ts1, Ts2
        result += f" T{Td}, T{Ts1}, T{Ts2}"
        return result

    def _format_tmv_vtr_alt_instruction(self, fields: Dict[str, int]) -> str:
        """Format alternate tmv.vtr.e32 instruction as standard tmv.vtr.e32"""
        result = "tmv.vtr.e32"

        # Extract operand fields
        vd = fields.get('vd', 0)
        Ts1 = fields.get('Ts1', 0)
        rs2 = fields.get('rs2', 0)

        # Format as tmv.vtr.e32 vd, Ts1, rs2/zero
        ops = [f"v{vd}", f"T{Ts1}"]
        if rs2 == 0:
            ops.append("zero")
        else:
            ops.append(f"x{rs2}")

        result += " " + ", ".join(ops)
        return result
    def _format_tmul_ttr_alt_instruction(self, fields: Dict[str, int]) -> str:
        """Format alternate tmul.ttr instruction as standard tmul.ttr.f32"""
        result = "tmul.ttr.f32"

        # Extract operand fields
        Td = fields.get('Td', 0)
        Ts1 = fields.get('Ts1', 0)
        rs2 = fields.get('rs2', 0)

        # Format as tmul.ttr.f32 Td, Ts1, rs2
        ops = [f"T{Td}", f"T{Ts1}", f"x{rs2}"]
        result += " " + ", ".join(ops)
    def _format_tmax_ttt_alt2_instruction(self, fields: Dict[str, int]) -> str:
        """Format alternate tmax.ttt instruction (encoding 2) as standard tmax.ttt.f32"""
        result = "tmax.ttt.f32"

        # Extract operand fields
        Td = fields.get('Td', 0)
        Ts1 = fields.get('Ts1', 0)
        Ts2 = fields.get('Ts2', 0)

        # Format as tmax.ttt.f32 Td, Ts1, Ts2
        ops = [f"T{Td}", f"T{Ts1}", f"T{Ts2}"]
        result += " " + ", ".join(ops)
        return result
    def disassemble_hex_string(self, hex_str: str) -> Optional[str]:
        """
        Disassemble a hexadecimal string

        Args:
            hex_str: Hexadecimal string (with or without 0x prefix)

        Returns:
            Assembly string or None if no match found
        """
        # Clean hex string
        hex_str = hex_str.strip().lower()
        if hex_str.startswith('0x'):
            hex_str = hex_str[2:]

        try:
            # Convert to integer
            binary_data = int(hex_str, 16)

            # Determine bit width based on hex string length
            hex_bits = len(hex_str) * 4

            # Try different bit widths in order of preference
            # Start with the exact bit width, then try standard widths
            possible_widths = []

            if hex_bits <= 32:
                possible_widths = [32]
            elif hex_bits <= 64:
                possible_widths = [64, 32]
            elif hex_bits <= 96:
                possible_widths = [96, 64, 32]
            elif hex_bits <= 128:
                possible_widths = [128, 96, 64, 32]
            else:
                # For very long hex strings, try all supported widths
                possible_widths = [128, 96, 64, 32]

            # Try each bit width until we find a match
            for bit_width in possible_widths:
                result = self.disassemble_instruction(binary_data, bit_width)
                if result:
                    return result

            return None

        except ValueError as e:
            print(f"Error: Invalid hex string '{hex_str}': {e}")
            return None

    def list_instructions(self) -> List[str]:
        """Return list of all known instruction names"""
        return sorted(self.instructions.keys())


def main():
    """Main function for command line usage"""
    if len(sys.argv) < 2:
        print("Usage: python tile_disassembler.py <hex_instruction> [hex_instruction2 ...]")
        print("       python tile_disassembler.py --list")
        print("")
        print("Examples:")
        print("  python tile_disassembler.py 0x1234567890abcdef")
        print("  python tile_disassembler.py 12345678")
        print("  python tile_disassembler.py --list")
        return

    disasm = TileDisassembler()

    if sys.argv[1] == "--list":
        print("Available instructions:")
        for instr in disasm.list_instructions():
            print(f"  {instr}")
        return

    # Disassemble provided hex strings
    for hex_str in sys.argv[1:]:
        result = disasm.disassemble_hex_string(hex_str)
        if result:
            print(f"{hex_str} -> {result}")
        else:
            print(f"{hex_str} -> Unknown instruction")


if __name__ == "__main__":
    main()
