Input Shape Mode,Ext_direct,output bits,micro_num,A Shape(MxK),B <PERSON>hape(NxK),<PERSON> Shape(MxN),Comp<PERSON> Shape(MxNxK),A TileRegNum(MxK),B TileRegNum(NxK),<PERSON> TileRegNum(MxN)
normal,No-extend,32 bit,0,16x64B,32x64B,16x32x32bit,16x32x64B,1(1x1),2(2x1),2(1x2)
normal,K-extend,32 bit,1,16x128B,32x128B,16x32x32bit,16x32x128B,2(1x2),4(2x2),2(1x2)
normal,K-extend,32 bit,2,16x256B,32x256B,16x32x32bit,16x32x256B,4(1x4),8(2x4),2(1x2)
A8W4,No-extend,32 bit,0,16x128B,32x64B,16x32x32bit,16x32x(128B_64B),2(1x2),2(2x1),2(1x2)
A8W4,K-extend,32 bit,1,16x256B,32x128B,16x32x32bit,16x32x(256B_128B),4(1x4),4(2x2),2(1x2)
A8W4,K-extend,32 bit,2,16x512B,32x256B,16x32x32bit,16x32x(512B_256B),8(1x8),8(2x4),2(1x2)
MXFP6,No-extend,32 bit,0,16x192B,32x192B,16x32x32bit,16x32x192B,3(1x3),6(2x3),2(1x2)
MXFP6,K-extend,32 bit,1,16x384B,32x384B,16x32x32bit,16x32x384B,6(1x6),12(2x6),2(1x2)
MXFP6,K-extend,32 bit,2,16x768B,32x768B,16x32x32bit,16x32x768B,12(1x12),24(2x12),2(1x2)
