```wavedrom
//tst.trii.linear.u32.share
{"reg": [
    {"bits": 7,  "name": 'ACE_op',    "attr": '1111011'},
    {"bits": 2,  "name": '',    "attr": '00'},
    {"bits": 1,  "name": 'tmask',        "attr": ''},
    {"bits": 2,  "name": 'lsuop',     "attr": '00'},
    {"bits": 3,  "name": 'tuop',     "attr": '110'},
    {"bits": 5,  "name": 'imm1',       "attr": ''},
    {"bits": 5,  "name": 'imm2',       "attr": ''},
    {"bits": 6,  "name": 'memuop', "attr":'001000'},
    {"bits": 1,  "name": '',    "attr": '0'},
    {"bits": 7,  "name": 'ACE_op',    "attr": '1111011'},
    {"bits": 5,  "name": '',        "attr": '00000'},
    {"bits": 3,  "name": 'tuop',     "attr": '001'},
    {"bits": 5,  "name": 'rs1',       "attr": ''},
    {"bits": 3,  "name": 'tilesize',       "attr": ''},
    {"bits": 8,  "name": 'Ts1',       "attr": ''},
    {"bits": 1,  "name": '',     "attr": '1'},
],config:{"bits":64,lanes:2,hspace: 1500}}
```
