```wavedrom
//thwid
{"reg": [
    {"bits": 2, "name": 'core_id', 'attr': 'RO'},
    {"bits": 27, "name": '', 'attr': 'RO'},
    {"bits": 2, "name": 'pe_id', 'attr': 'RO'},
    {"bits": 2, "name": '', 'attr': 'RO'},
    {"bits": 4, "name": 'cluster_id', 'attr': 'RO'},
    {"bits": 8, "name": 'chip_id', 'attr': 'RO'},
    {"bits": 20, "name": '', 'attr': 'RO'},
], config: {"bits":64,lanes:2,hspace: 1500}}
```