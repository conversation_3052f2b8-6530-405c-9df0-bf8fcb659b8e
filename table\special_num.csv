imm_encode,special_number,F32,F16,BF16
0,0,0x00000000,0x0000,0x0000
1,0.5,0x3f000000,0x3800,0x3f00
2,-0.5,0xbf000000,0xb800,0xbf00
3,1.0,0x3f800000,0x3c00,0x3f80
4,-1.0,0xbf800000,0xbc00,0xbf80
5,2.0,0x40000000,0x4000,0x4000
6,-2.0,0xc0000000,0xc000,0xc000
7,4.0,0x40800000,0x4400,0x4080
8,-4.0,0xc0800000,0xc400,0xc080
9, 1.0/(2*PI),0x3e22f983,0x3118,0x3e23
10, log2(e),0x3fb8aa3b,0x3dc5,0x3fb9
11, NaN,0x7fc00000,0x7e00,0x7fc0
12, Max,0x7f7fffff,0x7bff,0x7f7f
13, Min,0xff7fffff,0xfbff,0xff7f
14, 1e-5,3727c5ac,00a7,3728
15, 1e-6,358637bd,0010,3586