Input Shape Mode,Ext_direct,output bits,micro_num,A Shape(MxK),<PERSON> <PERSON>hape(NxK),<PERSON> Shape(MxN),Comp<PERSON> Shape(MxNxK),A TileRegNum(MxK),B TileRegNum(NxK),<PERSON> TileRegNum(MxN)
normal,No-extern,32bits,0,1x128B,32x128B,1x32x32bit,1x32x128B,1/8(1/8x1),4(4x1),1/8(1/8x1)
normal,K-extern,32bits,1,1x256B,32x256B,1x32x32bit,1x32x256B,1/4(1/8x2),8(4x2),1/8(1/8x1)
normal,K-extern,32bits,2,1x512B,32x512B,1x32x32bit,1x32x512B,1/2(1/8x4),16(4x4),1/8(1/8x1)
normal,K-extern,32bits,3,1x1024B,32x1024B,1x32x32bit,1x32x1024B,1(1/8x8),32(4x8),1/8(1/8x1)
A8W4,No-extern,32bits,0,1x256B,32x128B,1x32x32bit,1x32x(256B_128B),1/4(1/4x1),4(4x1),1/8(1/8x1)
A8W4,K-extern,32bits,1,1x512B,32x256B,1x32x32bit,1x32x(512B_256B),1/2(1/4x2),8(4x2),1/8(1/8x1)
A8W4,K-extern,32bits,2,1x1024B,32x512B,1x32x32bit,1x32x(1024B_512B),1(1/4x4),16(4x4),1/8(1/8x1)
A8W4,K-extern,32bits,3,1x2048B,32x1024B,1x32x32bit,1x32x(2048B_1024B),2(1/4x8),32(4x8),1/8(1/8x1)
MXFP6,No-extern,32bits,0,1x384B,32x384B,1x32x32bit,1x32x384B,3/8(1/8x3),12(4x3),1/8(1/8x1)
MXFP6,K-extern,32bits,1,1x768B,32x768B,1x32x32bit,1x32x768B,3/4(1/8x6),24(4x6),1/8(1/8x1)
MXFP6,K-extern,32bits,2,1x1536B,32x1536B,1x32x32bit,1x32x1536B,3/2(1/8x12),48(4x12),1/8(1/8x1)
MXFP6,K-extern,32bits,3,1x3072B,32x3072B,1x32x32bit,1x32x3072B,3(1/8x24),96(4x24),1/8(1/8x1)
