Version,Date,Owner,Description,Comment
,,,,,
0.1,2024.9.9,<PERSON>（张磊）,第一份草稿,
0.2,2024.10.9,<PERSON>（张磊）,加入已有汇编指令名称、加入部分指令描述；MMA和Memory Operation、CSR指令相对确定，其余的仍需要调整。,
0.3,2024.10.10,<PERSON>（张磊）,加入atomic指令描述。修改CSR列表，sync和talu以及tacp指令仍需修订。,
0.4,2024.10.12,<PERSON>（张磊）,加入ace_bsync/ace_nbsync指令简单描述，修复HAS review会议上提到的问题。,
0.5,2024.10.12,<PERSON>（张磊）,加入tacp指令简单描述，修改CSR lists。,
0.5.1,2024.10.25,<PERSON>（张磊）,tmma.trr/trt/ttr汇编指令名中的标量寄存器标识错误；stride/index load/store不支持多寄存器操作，删除标识(m2/m4)；删除tacp指令的share_cluster；修复move指令描述错误,
0.5.2,2024.10.31,<PERSON>（张磊）,加入3OP ALU指令；block load/store描述修改；删除浮点寄存器支持；move和alu指令中加入立即数支持；修复kerneldims csr的encoding
0.5.3,2024.11.6,Milo Zhang（张磊）,CSR中加入private_base/size。twait.ls/tacp_cg cnt设置为8bits。sfu_uop中加入tanh。transpose加入M16N16模式。特殊值加入1.0/(2*PI)和log2(e)
0.5.4,2024.11.8,Milo Zhang（张磊）,修复tfma.ttri/ttrr/ttrt encoding。Special Value加入NaN。Conversion指令加入non-MX类型。
0.5.5,2024.11.19,Milo Zhang（张磊）,删除atomic指令中所有的tmask标志；删除scalar broadcast/tile atomic的vec_size标记，也不再支持v2/v4；atomic cas也不再支持v4; 加入tacp.commit_group指令；修复tacp encoding问题；加入tkill指令；tsync/twait相关指令encoding修改
0.5.6,2024.12.10,Milo Zhang（张磊）,修改tctrl csr；unit-stride load/store支持m8；sfu指令不支持neg取反标志；
0.6.0,2024.12.13,Milo Zhang（张磊）,MMA指令描述修改，Block Load/Store的stride mode和blk_index删除，convert指令支持relu
0.7.0,2024.12.16,Milo Zhang（张磊）,修改TACP和同步类指令的汇编名称，添加mbarrier类控制指令。
0.8.0,2024.12.20,Milo Zhang（张磊）,修改unit-stride和conversion指令，加入GEMV指令。special value支持1e-5 1e-6
0.8.1,2024.12.24,Milo Zhang（张磊）,move指令的index改为由scalar reg索引，取消立即数索引方式。Convert指令描述和汇编名修改。
0.8.2,2024.12.27,Milo Zhang（张磊）,CSR列表修改，删除ERT和Kenrel用不到的信息。删除GEMV以RVV输入的模式。
0.8.3,2024.12.31,Milo Zhang（张磊）,CSR列表修改，增加page table相关信息，增加debug用到的信息。删除tmma.mem的micro_num支持。
0.8.4,2025.1.6,Milo Zhang（张磊）,修改TACP指令名。Tile Reg的汇编名由ts1/ts2/ts3/td改为Ts1/Ts2/Ts3/Td，指令中可选项由小括号改为中括号表达。
0.8.5,2025.1.8,Milo Zhang（张磊）,mma中的shape字段改名为ashape。mem指令中所有访存的操作数都加上小括号。convert指令中加入整形rounding名称
0.8.6,2025.1.13,Milo Zhang（张磊）,atom.cas指令只支持x1。transpose指令不再支持neg1 bit。修复tvma.ttr指令的uop列表错误。tmma.mem类指令都被twait视为一个share memory load指令。CSR列表中加入paramsize。
0.8.7,2025.1.17,Milo Zhang（张磊）,atomc.cas指令名澄清为casb32，去除casb32v2支持。twait.tacp_cg改为有GPR输入。
0.8.8,2025.1.23,Milo Zhang（张磊）,加入用于标量输入控制的twait.r.ls指令。删除fp16->bf16并加入fp16->fp32/bf16->fp32的conversion。tctrl加入MX 非OCP mode模式的支持。CSR列表和mapping修改。
0.9.0,2025.2.21,Milo Zhang（张磊）,mma指令移除fp16/bf16作为输出结果的支持。csr指令的汇编名操作统一将csr_addr认为是最后一个操作数。
0.9.1,2025.2.24,Milo Zhang（张磊）,tmv.rtr和tmv.trr指令encoding修改，57bit为1（Andes COPILOT工具不支持原有encoding。）
0.9.2,2025.3.14,Milo Zhang（张磊）,加入tmbar.rr.dec_tx_cnt指令；
0.9.3,2025.3.20,Milo Zhang（张磊）,1.加入f32->(u)int8/4的conversion指令;2.所有的conversion指令支持后处理ReLU操作;3.所有的add/mul/fma指令可用csr配置后处理ReLU操作;4.M1N64(GEMV)和M8N64的指令改为M1N32和M8N32的指令
0.9.4,2025.4.2,Milo Zhang（张磊）,1.加入remote load/store/atomic/mbarrier指令;2.tacp指令的location bits改为remote bit;3.加入remote fence指令;4.csr中删除estatus寄存器，在SMU中实现
0.9.5,2025.4.7,Milo Zhang（张磊）,1.修改trr/rtr的汇编编码；2.在tmv.tvr/vtr指令区分元素宽度（EEW）;3.修改tacp指令中的tensormap编码
0.9.6,2025.4.25,Milo Zhang（张磊）,1.增加tile寄存器间move的tmv.ttrr指令;2.修复index load/store和tacp指令中的aspin指令汇编，约束vsetvl指令仅能使用SEW=32/LMUL=1/VL=32;3.修改scalar和tile Reg的move指令操作粒度为32B.
1.0.0,2025.4.30,Milo Zhang（张磊）,1.根据SIPU 1.5指令修改综述的讨论进行描述修改