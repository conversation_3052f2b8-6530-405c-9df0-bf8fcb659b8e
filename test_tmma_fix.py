#!/usr/bin/env python3

def test_tmma_instruction():
    """Test the tmma instruction decoding"""
    
    # Test instruction: 0x2337b0104627b
    # Expected: tmma.ttt.f32.mxint8.mxint8.r32.m4.reused T0,T4,T8
    
    hex_val = 0x2337b0104627b
    print(f"Testing instruction: 0x{hex_val:016x}")
    
    # Simulate the SystemVerilog logic
    word1 = hex_val & 0xFFFFFFFF
    word2 = (hex_val >> 32) & 0xFFFFFFFF
    
    print(f"Word 1: 0x{word1:08x}")
    print(f"Word 2: 0x{word2:08x}")
    print()
    
    # Check if it's detected as a tile instruction
    ace_op = word1 & 0x7F
    tuop_first = (word1 >> 12) & 0x7
    memuop = (word1 >> 25) & 0x3F
    tuop_second = (word2 >> 12) & 0x7
    
    print(f"ACE_OP: {ace_op} (should be 123)")
    print(f"TUOP first: {tuop_first} (should be 6)")
    print(f"memuop: {memuop} (should be 0)")
    print(f"TUOP second: {tuop_second} (should be 3)")
    print()
    
    # Check if it matches tmma pattern
    is_tile = ace_op == 123
    is_tuop_110 = tuop_first == 6
    is_memuop_000000 = memuop == 0
    is_tuop_011 = tuop_second == 3
    
    print(f"Is tile instruction: {is_tile}")
    print(f"Is tuop_110: {is_tuop_110}")
    print(f"Is memuop 000000: {is_memuop_000000}")
    print(f"Is tuop_011: {is_tuop_011}")
    print()
    
    if is_tile and is_tuop_110 and is_memuop_000000 and is_tuop_011:
        print("✓ Should be detected as tmma instruction")
        
        # Extract tmma fields
        memb = (word1 >> 7) & 0x1
        mop_5 = (word1 >> 9) & 0x1
        mop_4_0 = (word1 >> 26) & 0x1F
        mop_full = (mop_5 << 5) | mop_4_0
        reuse = (word2 >> 8) & 0x3
        ashape = (word2 >> 10) & 0x3
        mema = (word2 >> 31) & 0x1
        
        # Extract operands - let me double check the bit positions
        td = (word2 >> 23) & 0xFF
        ts1 = (word2 >> 15) & 0xFF
        ts2 = (word1 >> 15) & 0xFF

        print(f"Detailed field extraction:")
        print(f"word2 bits 30:23 = {(word2 >> 23) & 0xFF:08b} = {(word2 >> 23) & 0xFF}")
        print(f"word2 bits 22:15 = {(word2 >> 15) & 0xFF:08b} = {(word2 >> 15) & 0xFF}")
        print(f"word1 bits 22:15 = {(word1 >> 15) & 0xFF:08b} = {(word1 >> 15) & 0xFF}")
        print(f"word2 bits 11:10 (ashape) = {(word2 >> 10) & 0x3:02b} = {(word2 >> 10) & 0x3}")
        print(f"word2 bits 9:8 (reuse) = {(word2 >> 8) & 0x3:02b} = {(word2 >> 8) & 0x3}")
        print()
        
        print(f"memb: {memb}")
        print(f"mema: {mema}")
        print(f"mop: {mop_full}")
        print(f"reuse: {reuse}")
        print(f"ashape: {ashape}")
        print(f"Td: {td}")
        print(f"Ts1: {ts1}")
        print(f"Ts2: {ts2}")
        print()
        
        # Determine variant
        if mema == 0 and memb == 0:
            variant = "tmma.ttt"
        elif mema == 0 and memb == 1:
            variant = "tmma.ttr"
        elif mema == 1 and memb == 0:
            variant = "tmma.trt"
        else:
            variant = "tmma.trr"
        
        # Determine data type
        data_types = {
            0: "tf32.tf32.f32",
            1: "f16.f16.f32",
            3: "bf16.bf16.f32",
            32: "f32.mxint8.mxint8"
        }
        data_type = data_types.get(mop_full, "unknown")
        
        # Determine reuse - let me check what reuse=3 should be
        reuse_strs = {
            0: "r32",
            1: "reused",
            2: "reused",
            3: "reused"  # Add mapping for 3
        }
        reuse_str = reuse_strs.get(reuse, f"unknown_{reuse}")
        
        # Determine ashape
        ashape_strs = {
            0: "m8",
            1: "m16", 
            2: "m4",
            3: "m32"
        }
        ashape_str = ashape_strs.get(ashape, "unknown")
        
        instruction_name = f"{variant}.{data_type}.{reuse_str}.{ashape_str}"
        
        if memb == 0:
            operands = f"T{td}, T{ts1}, T{ts2}"
        else:
            rs2 = (word1 >> 20) & 0x1F
            operands = f"T{td}, T{ts1}, x{rs2}"
        
        print(f"Generated instruction: {instruction_name} {operands}")
        print(f"Expected instruction: tmma.ttt.f32.mxint8.mxint8.r32.m4.reused T0,T4,T8")

        # Check matches
        actual_parts = instruction_name.split('.')
        print(f"Actual parts: {actual_parts}")

        print()
        print("Comparison:")
        print(f"Variant: {actual_parts[0]} vs tmma.ttt {'✓' if actual_parts[0] == 'tmma.ttt' else '✗'}")
        print(f"Data type: {actual_parts[1]} vs f32.mxint8.mxint8 {'✓' if actual_parts[1] == 'f32.mxint8.mxint8' else '✗'}")
        print(f"Reuse: {actual_parts[2]} vs reused {'✓' if actual_parts[2] == 'reused' else '✗'}")
        print(f"Ashape: {actual_parts[3]} vs m4 {'✓' if actual_parts[3] == 'm4' else '✗'}")
        print(f"Operands: {operands} vs T0,T4,T8 {'✓' if operands == 'T0, T4, T8' else '✗'}")

        # The issue is that ashape=0 gives m8, but user expects m4
        # Let me check if I'm extracting ashape from the wrong bits
        print()
        print("Debugging ashape field:")
        print(f"User expects m4, which should be ashape=2")
        print(f"But I'm getting ashape=0 from bits 11:10 of word2")
        print(f"Let me check other possible bit positions for ashape=2:")

        for i in range(0, 32, 2):
            field_val = (word2 >> i) & 0x3
            if field_val == 2:
                print(f"  Found ashape=2 at word2 bits {i+1}:{i}")

        for i in range(0, 32, 2):
            field_val = (word1 >> i) & 0x3
            if field_val == 2:
                print(f"  Found ashape=2 at word1 bits {i+1}:{i}")
        
    else:
        print("✗ Would not be detected as tmma instruction")

if __name__ == '__main__':
    test_tmma_instruction()
