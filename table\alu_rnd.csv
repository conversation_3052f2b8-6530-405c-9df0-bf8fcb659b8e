ALU Inst,Data Types,TIRND 2bit,TFRND 3bit
FMA,F32,Not Support,"{ .rne, .rtz, .rtn, .rtp, }"
FMA,F16,Not Support,"{ .rne, .rtz, .rtn, .rtp, }"
FMA,BF16,Not Support,"{ .rne, .rtz, .rtn, .rtp, }"
MUL,F32,Not Support,"{ .rne, .rtz, .rtn, .rtp, }"
MUL,F16,Not Support,"{ .rne, .rtz, .rtn, .rtp, }"
MUL,BF16,Not Support,"{ .rne, .rtz, .rtn, .rtp, }"
ADD,F32,Not Support,"{ .rne, .rtz, .rtn, .rtp, }"
ADD,F16,Not Support,"{ .rne, .rtz, .rtn, .rtp, }"
ADD,BF16,Not Support,"{ .rne, .rtz, .rtn, .rtp, }"
CVT(All From ISA),F32_BF16,Not Support,"{ .rne, .rtz, .rtn, .rtp, }"
CVT(All From ISA),F32_F16,Not Support,"{ .rne, .rtz, .rtn, .rtp, }"
CVT(All From ISA),F16_F32,Not Support,"{ .rne, .rtz, .rtn, .rtp, }"
CVT(All From ISA),BF16_F32,Not Support,"{ .rne, .rtz, .rtn, .rtp, }"
CVT(All From ISA),S32_F32,Not Support,"{ .rne, .rtz, .rtn, .rtp, }"
CVT(All From ISA),S32_BF16,Not Support,"{ .rne, .rtz, .rtn, .rtp, }"
CVT(All From ISA),BF16_(U)INT8/4,"{ .rni, .rzi, .rmi, .rpi }",Not Support
CVT(All From ISA),F32_FP8,Not Support,{ .rne }
CVT(All From ISA),F16_FP8,Not Support,{ .rne }
CVT(All From ISA),BF16_FP8,Not Support,{ .rne }
CVT(All From ISA),F32_MXFP8/6/4,Not Support,{ .rne }
CVT(All From ISA),F16_MXFP8/6/4,Not Support,{ .rne }
CVT(All From ISA),BF16_MXFP8/6/4,Not Support,{ .rne }
CVT(All From ISA),F32_MXINT8/4,Not Support,{ .rne }
CVT(All From ISA),F16_MXINT8/4,Not Support,{ .rne }
CVT(All From ISA),BF16_MXINT8/4,Not Support,{ .rne }
