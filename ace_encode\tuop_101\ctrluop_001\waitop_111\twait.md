```wavedrom
//twait
{"reg": [
    {"bits": 7,  "name": 'ACE_op',    "attr": '1111011'},
    {"bits": 5,  "name": '',       "attr": '00000'},
    {"bits": 3,  "name": 'tuop',     "attr": '101'},
    {"bits": 8,  "name": '',       "attr": '00000000'},
    {"bits": 3,  "name": 'ctrluop',    "attr": '001'},
    {"bits": 1,  "name": 'isMem',        "attr": ''},
    {"bits": 1,  "name": '',        "attr": '0'},
    {"bits": 3,  "name": 'waitop',    "attr": '111'},
    {"bits": 1,  "name": '',    "attr": '0'},
],config:{"bits":32,lanes:1,hspace: 1500}}
```