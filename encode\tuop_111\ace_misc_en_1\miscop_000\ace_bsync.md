```wavedrom
//ace_bsync
{"reg": [
    {"bits": 7,  "name": 'ACE_op',    "attr": '1111011'},
    {"bits": 5,  "name": '',    "attr": '00000'},
    {"bits": 3,  "name": 'tuop',     "attr": '111'},
    {"bits": 5,  "name": 'sync_id',    "attr": ''},
    {"bits": 8,  "name": '',    "attr": '00000000'},
    {"bits": 3,  "name": 'miscop',       "attr": '000'},
    {"bits": 1,  "name": '',    "attr": '1'},
],config:{"bits":32,lanes:1,hspace: 1500}}
```