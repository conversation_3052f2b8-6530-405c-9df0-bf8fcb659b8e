#!/usr/bin/env python3

def reverse_engineer_tmma():
    """Reverse engineer the correct tmma instruction encoding"""
    
    # User expects: tmma.ttt.f32.mxint8.mxint8.r32.m4.reused T0,T4,T8
    # Actual instruction: 0x2337b0104627b
    
    hex_val = 0x2337b0104627b
    print(f"Instruction: 0x{hex_val:016x}")
    print(f"Binary: {hex_val:064b}")
    print()
    
    word1 = hex_val & 0xFFFFFFFF
    word2 = (hex_val >> 32) & 0xFFFFFFFF
    print(f"Word 1: 0x{word1:08x} = {word1:032b}")
    print(f"Word 2: 0x{word2:08x} = {word2:032b}")
    print()
    
    # Expected values:
    # - Variant: tmma.ttt (memmata=0, memmatb=0)
    # - Data type: f32.mxint8.mxint8 (mop=32 according to table)
    # - Reuse: reused (reuse=01 or 10?)
    # - A shape: m4 (ashape=10)
    # - Operands: T0, T4, T8
    
    print("Expected values:")
    print("- memmata: 0")
    print("- memmatb: 0") 
    print("- mop: 32 (for f32.mxint8.mxint8)")
    print("- reuse: reused")
    print("- ashape: 10 (m4)")
    print("- Td: 0")
    print("- Ts1: 4") 
    print("- Ts2: 8")
    print()
    
    # Let's check if mop=32 can be found anywhere
    print("Searching for mop=32 (100000 in binary):")
    
    # Check 6-bit fields
    for i in range(0, 64-5):
        field_val = (hex_val >> i) & 0x3F
        if field_val == 32:
            print(f"  Found mop=32 at bits {i+5}:{i}")
    
    print()
    
    # Let's try a different approach - maybe the instruction is encoded differently
    # Let me check if there's a pattern that makes sense
    
    print("Analyzing based on wavedrom structure:")
    print("According to tmma.ttt.md, the structure should be:")
    print("Word 1: ACE_op(7) + memb(1) + ''(1) + mop[5](1) + noacc(1) + neg(1) + tuop(3) + Ts2(8) + micro_num(2) + gemv(1) + mop[4:0](5) + ''(1)")
    print("Word 2: ACE_op(7) + ''(1) + reuse(2) + ashape(2) + tuop(3) + Ts1(8) + Td(8) + mema(1)")
    print()
    
    # Extract fields according to this structure
    # Word 1 fields (from LSB to MSB):
    ace_op1 = word1 & 0x7F  # bits 6:0
    memb = (word1 >> 7) & 0x1  # bit 7
    reserved1 = (word1 >> 8) & 0x1  # bit 8
    mop_5 = (word1 >> 9) & 0x1  # bit 9
    noacc = (word1 >> 10) & 0x1  # bit 10
    neg = (word1 >> 11) & 0x1  # bit 11
    tuop1 = (word1 >> 12) & 0x7  # bits 14:12
    ts2 = (word1 >> 15) & 0xFF  # bits 22:15
    micro_num = (word1 >> 23) & 0x3  # bits 24:23
    gemv = (word1 >> 25) & 0x1  # bit 25
    mop_4_0 = (word1 >> 26) & 0x1F  # bits 30:26
    reserved2 = (word1 >> 31) & 0x1  # bit 31
    
    # Word 2 fields (from LSB to MSB):
    ace_op2 = word2 & 0x7F  # bits 6:0
    reserved3 = (word2 >> 7) & 0x1  # bit 7
    reuse = (word2 >> 8) & 0x3  # bits 9:8
    ashape = (word2 >> 10) & 0x3  # bits 11:10
    tuop2 = (word2 >> 12) & 0x7  # bits 14:12
    ts1 = (word2 >> 15) & 0xFF  # bits 22:15
    td = (word2 >> 23) & 0xFF  # bits 30:23
    mema = (word2 >> 31) & 0x1  # bit 31
    
    mop_full = (mop_5 << 5) | mop_4_0
    
    print("Extracted fields:")
    print(f"ACE_OP1: {ace_op1:07b} = {ace_op1}")
    print(f"memb: {memb}")
    print(f"mop[5]: {mop_5}")
    print(f"noacc: {noacc}")
    print(f"neg: {neg}")
    print(f"tuop1: {tuop1:03b} = {tuop1}")
    print(f"Ts2: {ts2:08b} = T{ts2}")
    print(f"micro_num: {micro_num:02b}")
    print(f"gemv: {gemv}")
    print(f"mop[4:0]: {mop_4_0:05b} = {mop_4_0}")
    print(f"Full mop: {mop_full:06b} = {mop_full}")
    print()
    print(f"ACE_OP2: {ace_op2:07b} = {ace_op2}")
    print(f"reuse: {reuse:02b} = {reuse}")
    print(f"ashape: {ashape:02b} = {ashape}")
    print(f"tuop2: {tuop2:03b} = {tuop2}")
    print(f"Ts1: {ts1:08b} = T{ts1}")
    print(f"Td: {td:08b} = T{td}")
    print(f"mema: {mema}")
    print()
    
    # Check if this matches expectations
    print("Checking against expectations:")
    print(f"memmata (mema): {mema} (expected: 0) {'✓' if mema == 0 else '✗'}")
    print(f"memmatb (memb): {memb} (expected: 0) {'✓' if memb == 0 else '✗'}")
    print(f"mop: {mop_full} (expected: 32) {'✓' if mop_full == 32 else '✗'}")
    print(f"ashape: {ashape} (expected: 2 for m4) {'✓' if ashape == 2 else '✗'}")
    print(f"Td: {td} (expected: 0) {'✓' if td == 0 else '✗'}")
    print(f"Ts1: {ts1} (expected: 4) {'✓' if ts1 == 4 else '✗'}")
    print(f"Ts2: {ts2} (expected: 8) {'✓' if ts2 == 8 else '✗'}")

if __name__ == '__main__':
    reverse_engineer_tmma()
