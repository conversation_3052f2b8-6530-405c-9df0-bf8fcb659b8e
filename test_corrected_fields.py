#!/usr/bin/env python3

def test_corrected_tmma_fields():
    """Test the corrected tmma field interpretation"""
    
    print("=== Testing Corrected TMMA Field Interpretation ===")
    print()
    
    # Test instruction: 0x2337b0104627b
    # Expected: tmma.ttt.f32.mxint8.mxint8.r32.m4.reused T0,T4,T8
    hex_val = 0x2337b0104627b
    print(f"Test instruction: 0x{hex_val:016x}")
    
    word1 = hex_val & 0xFFFFFFFF
    word2 = (hex_val >> 32) & 0xFFFFFFFF
    
    print(f"Word 1: 0x{word1:08x} = {word1:032b}")
    print(f"Word 2: 0x{word2:08x} = {word2:032b}")
    print()
    
    # Extract fields according to corrected understanding
    memb = (word1 >> 7) & 0x1
    mop_5 = (word1 >> 9) & 0x1
    mop_4_0 = (word1 >> 26) & 0x1F
    mop_full = (mop_5 << 5) | mop_4_0
    micro_num = (word1 >> 23) & 0x3  # Corrected: micro_num from word1[24:23]
    reuse = (word2 >> 8) & 0x3
    ashape = (word2 >> 10) & 0x3     # Corrected: ashape is r32/r16/r8
    mema = (word2 >> 31) & 0x1
    
    # Extract operands
    td = (word2 >> 23) & 0xFF
    ts1 = (word2 >> 15) & 0xFF
    ts2 = (word1 >> 15) & 0xFF
    
    print("Extracted Fields:")
    print(f"  memb: {memb}")
    print(f"  mema: {mema}")
    print(f"  mop_full: {mop_full}")
    print(f"  micro_num: {micro_num:02b} = {micro_num}")
    print(f"  reuse: {reuse:02b} = {reuse}")
    print(f"  ashape: {ashape:02b} = {ashape}")
    print(f"  Td: {td}")
    print(f"  Ts1: {ts1}")
    print(f"  Ts2: {ts2}")
    print()
    
    # Determine instruction components
    if mema == 0 and memb == 0:
        variant = "tmma.ttt"
    elif mema == 0 and memb == 1:
        variant = "tmma.ttr"
    elif mema == 1 and memb == 0:
        variant = "tmma.trt"
    else:
        variant = "tmma.trr"
    
    # Data type based on mop
    data_types = {
        0: "tf32.tf32.f32",
        1: "f16.f16.f32",
        3: "bf16.bf16.f32",
        32: "f32.mxint8.mxint8"
    }
    data_type = data_types.get(mop_full, f"mop_{mop_full}")
    
    # Corrected: ashape is r32/r16/r8
    ashape_strs = {
        0: "r32",
        1: "r16",
        2: "r8",
        3: "unknown_ashape"
    }
    ashape_str = ashape_strs.get(ashape, f"ashape_{ashape}")
    
    # Corrected: micro_num is m1/m2/m4
    micro_strs = {
        0: "m1",
        1: "m2",
        2: "m4",
        3: "unknown_micro"
    }
    micro_str = micro_strs.get(micro_num, f"micro_{micro_num}")
    
    # reuse field
    reuse_strs = {
        0: "r32",
        1: "reused",
        2: "reused",
        3: "reused"
    }
    reuse_str = reuse_strs.get(reuse, f"reuse_{reuse}")
    
    # Build instruction name with corrected order
    instruction_name = f"{variant}.{data_type}.{ashape_str}.{micro_str}.{reuse_str}"
    
    if memb == 0:
        operands = f"T{td}, T{ts1}, T{ts2}"
    else:
        rs2 = (word1 >> 20) & 0x1F
        operands = f"T{td}, T{ts1}, x{rs2}"
    
    print("Generated Instruction:")
    print(f"  {instruction_name} {operands}")
    print()
    
    print("User Expected:")
    print(f"  tmma.ttt.f32.mxint8.mxint8.r32.m4.reused T0,T4,T8")
    print()
    
    # Detailed comparison
    actual_parts = instruction_name.split('.')
    print(f"Actual parts: {actual_parts}")
    print(f"Number of parts: {len(actual_parts)}")

    print("Detailed Comparison:")
    print(f"  Full instruction: {instruction_name}")
    print(f"  Expected format: tmma.ttt.f32.mxint8.mxint8.r32.m4.reused")
    print()

    # Check individual components
    print(f"  Variant: {variant} vs tmma.ttt {'✓' if variant == 'tmma.ttt' else '✗'}")
    print(f"  Data type: {data_type} vs f32.mxint8.mxint8 {'✓' if data_type == 'f32.mxint8.mxint8' else '✗'}")
    print(f"  Ashape (r32/r16/r8): {ashape_str} vs r32 {'✓' if ashape_str == 'r32' else '✗'}")
    print(f"  Micro_num (m1/m2/m4): {micro_str} vs m4 {'✓' if micro_str == 'm4' else '✗'}")
    print(f"  Reuse: {reuse_str} vs reused {'✓' if reuse_str == 'reused' else '✗'}")
    print(f"  Operands: {operands} vs T0,T4,T8 {'✓' if operands.replace(' ', '') == 'T0,T4,T8' else '✗'}")
    print()

    # Check if all fields match expectations
    all_match = (
        variant == "tmma.ttt" and
        data_type == "f32.mxint8.mxint8" and
        ashape_str == "r32" and
        micro_str == "m4" and
        reuse_str == "reused" and
        operands.replace(' ', '') == "T0,T4,T8"
    )
    
    print("=== FINAL RESULT ===")
    if all_match:
        print("✅ SUCCESS: All fields match user expectations!")
        print("✅ The corrected field interpretation is working correctly.")
    else:
        print("❌ Some fields still don't match. Let me debug:")
        print(f"   ashape field value: {ashape} (bits {ashape:02b})")
        print(f"   micro_num field value: {micro_num} (bits {micro_num:02b})")
        print(f"   Expected ashape=0 for r32, got: {ashape}")
        print(f"   Expected micro_num=2 for m4, got: {micro_num}")
        
        # Let me check if the expected values are in different bit positions
        print()
        print("Searching for expected field values in other positions:")
        print("Looking for ashape=0 (for r32):")
        for i in range(0, 32, 2):
            val = (word2 >> i) & 0x3
            if val == 0:
                print(f"  Found ashape=0 at word2 bits {i+1}:{i}")
        
        print("Looking for micro_num=2 (for m4):")
        for i in range(0, 32, 2):
            val = (word1 >> i) & 0x3
            if val == 2:
                print(f"  Found micro_num=2 at word1 bits {i+1}:{i}")

if __name__ == '__main__':
    test_corrected_tmma_fields()
