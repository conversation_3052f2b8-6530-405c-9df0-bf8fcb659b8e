#!/usr/bin/env python3
"""
测试wavedrom解析是否正确
"""

import yaml

def test_tmul_ttr_parsing():
    """测试tmul.ttr的wavedrom解析"""
    
    # tmul.ttr的wavedrom定义
    wavedrom_content = """
{"reg": [
    {"bits": 7,  "name": "ACE_op",    "attr": "1111011"},
    {"bits": 2,  "name": "",    "attr": "01"},
    {"bits": 2,  "name": "vecuop1",    "attr": "10"},
    {"bits": 1,  "name": "tmask",    "attr": ""},
    {"bits": 3,  "name": "tuop",     "attr": "110"},
    {"bits": 5,  "name": "rs2",    "attr": ""},
    {"bits": 5,  "name": "",     "attr": "00000"},
    {"bits": 1,  "name": "reuse1",    "attr": ""},
    {"bits": 1,  "name": "",    "attr": "0"},
    {"bits": 1,  "name": "neg1",    "attr": ""},
    {"bits": 1,  "name": "neg2",    "attr": ""},
    {"bits": 2,  "name": "vecuop2[1:0]",       "attr": ""},
    {"bits": 1,  "name": "",    "attr": "0"},
    {"bits": 7,  "name": "ACE_op",    "attr": "1111011"},
    {"bits": 1,  "name": "",        "attr": "0"},
    {"bits": 4,  "name": "vecuop2[5:2]",    "attr": "0001"},
    {"bits": 3,  "name": "tuop",     "attr": "010"},
    {"bits": 8,  "name": "Ts1",       "attr": ""},
    {"bits": 8,  "name": "Td",       "attr": ""},
    {"bits": 1,  "name": "",     "attr": "0"}
]}
"""
    
    data = yaml.safe_load(wavedrom_content)
    
    print("tmul.ttr wavedrom字段解析:")
    print("字段从LSB到MSB的顺序:")
    
    pos = 0
    total_bits = 0
    for field_data in data["reg"]:
        total_bits += field_data.get("bits", 0)
    
    print(f"总位数: {total_bits}")
    print()
    
    # 计算每个字段的位置
    pos = 0
    for i, field_data in enumerate(data["reg"]):
        bits = field_data.get("bits", 0)
        name = field_data.get("name", "")
        attr = field_data.get("attr", "")
        
        end_bit = pos + bits - 1
        start_bit = pos
        
        print(f"{i:2d}: {name:15s}[{end_bit:2d}:{start_bit:2d}] = \"{attr}\" ({bits} bits)")
        pos += bits
    
    print()
    
    # 构建期望的编码模式
    encoding = ""
    match_value = 0
    match_mask = 0
    
    pos = 0
    for field_data in data["reg"]:
        bits = field_data.get("bits", 0)
        attr = field_data.get("attr", "")
        
        if attr:
            # 固定位
            field_value = int(attr, 2) if all(c in '01' for c in attr) else 0
            match_value |= (field_value << pos)
            match_mask |= (((1 << bits) - 1) << pos)
        
        pos += bits
    
    print(f"计算出的匹配值: 0x{match_value:016x}")
    print(f"计算出的匹配掩码: 0x{match_mask:016x}")
    print()
    
    # 测试我们的指令
    test_instruction = 0x82216b000564fb
    print(f"测试指令: 0x{test_instruction:016x}")
    
    masked_instruction = test_instruction & match_mask
    print(f"指令 & 掩码: 0x{masked_instruction:016x}")
    print(f"匹配结果: {'✓' if masked_instruction == match_value else '✗'}")
    
    if masked_instruction != match_value:
        print("\n字段级别的比较:")
        pos = 0
        for field_data in data["reg"]:
            bits = field_data.get("bits", 0)
            name = field_data.get("name", "")
            attr = field_data.get("attr", "")
            
            if attr and name:  # 只检查有名称的固定字段
                field_mask = (1 << bits) - 1
                actual_value = (test_instruction >> pos) & field_mask
                expected_value = int(attr, 2) if all(c in '01' for c in attr) else 0
                
                match = actual_value == expected_value
                print(f"  {name:15s}[{pos+bits-1:2d}:{pos:2d}]: 实际 {actual_value:0{bits}b}, 期望 {attr} {'✓' if match else '✗'}")
            
            pos += bits

def manual_field_extraction():
    """手动提取字段值"""
    
    test_instruction = 0x82216b000564fb
    print(f"\n手动字段提取 - 指令: 0x{test_instruction:016x}")
    
    # 根据wavedrom定义手动提取关键字段
    ace_op1 = test_instruction & 0x7F  # bits [6:0]
    bits_8_7 = (test_instruction >> 7) & 0x3  # bits [8:7]
    vecuop1 = (test_instruction >> 9) & 0x3  # bits [10:9]
    tuop1 = (test_instruction >> 12) & 0x7  # bits [14:12]
    
    # 第二个word的字段
    ace_op2 = (test_instruction >> 32) & 0x7F  # bits [38:32]
    vecuop2_52 = (test_instruction >> (32+8)) & 0xF  # bits [43:40]
    tuop2 = (test_instruction >> (32+12)) & 0x7  # bits [46:44]
    
    print(f"ACE_op1[6:0]: {ace_op1:07b} = 0x{ace_op1:02x} (期望 1111011)")
    print(f"bits[8:7]: {bits_8_7:02b} (期望 01)")
    print(f"vecuop1[10:9]: {vecuop1:02b} (期望 10)")
    print(f"tuop1[14:12]: {tuop1:03b} (期望 110)")
    print(f"ACE_op2[38:32]: {ace_op2:07b} = 0x{ace_op2:02x} (期望 1111011)")
    print(f"vecuop2[5:2][43:40]: {vecuop2_52:04b} (期望 0001)")
    print(f"tuop2[46:44]: {tuop2:03b} (期望 010)")
    
    print("\n匹配检查:")
    checks = [
        (ace_op1 == 0b1111011, "ACE_op1"),
        (bits_8_7 == 0b01, "bits[8:7]"),
        (vecuop1 == 0b10, "vecuop1"),
        (tuop1 == 0b110, "tuop1"),
        (ace_op2 == 0b1111011, "ACE_op2"),
        (vecuop2_52 == 0b0001, "vecuop2[5:2]"),
        (tuop2 == 0b010, "tuop2")
    ]
    
    all_match = True
    for match, name in checks:
        print(f"  {name}: {'✓' if match else '✗'}")
        if not match:
            all_match = False
    
    print(f"\n总体匹配: {'✓' if all_match else '✗'}")
    
    if all_match:
        print("指令应该匹配tmul.ttr！")
    else:
        print("指令不匹配tmul.ttr，可能是其他指令或编码错误")

if __name__ == "__main__":
    test_tmul_ttr_parsing()
    manual_field_extraction()
