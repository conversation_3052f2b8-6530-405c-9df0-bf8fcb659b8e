```wavedrom
//tmul.ttr.alt
{"reg": [
    {"bits": 7,  "name": 'ACE_op',    "attr": '1111011'},
    {"bits": 2,  "name": 'rsen_immen',    "attr": '10'},
    {"bits": 2,  "name": 'vecuop1',    "attr": '10'},
    {"bits": 1,  "name": 'tmask',    "attr": ''},
    {"bits": 3,  "name": 'tuop',     "attr": '110'},
    {"bits": 5,  "name": 'rs2',    "attr": ''},
    {"bits": 3,  "name": '',    "attr": '000'},
    {"bits": 8,  "name": 'Ts2',       "attr": ''},
    {"bits": 1,  "name": '',    "attr": '0'},
    {"bits": 7,  "name": 'ACE_op',    "attr": '1111011'},
    {"bits": 8,  "name": 'Td',        "attr": ''},
    {"bits": 3,  "name": 'tuop',     "attr": '010'},
    {"bits": 1,  "name": '',       "attr": '0'},
    {"bits": 4,  "name": '',    "attr": '0011'},
    {"bits": 8,  "name": 'Ts1',       "attr": ''},
    {"bits": 1,  "name": '',     "attr": '0'},
],config:{"bits":64,lanes:2,hspace: 1500}}
```
